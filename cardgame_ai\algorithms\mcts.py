"""
蒙特卡洛树搜索 (MCTS) 算法

实现蒙特卡洛树搜索算法及其变体，包括MuZero风格的MCTS。
支持不完全信息游戏和多人游戏。
"""

import time
import math
import random
import logging
from typing import Dict, List, Optional, Tuple, Union, Any
import numpy as np
import torch

from cardgame_ai.algorithms.information_value import calculate_information_value
# 更新导入路径，使用组件目录下的 KeyMomentDetector
from cardgame_ai.algorithms.components.key_moment_detector import KeyMomentDetector
from cardgame_ai.games.common.belief_state import BeliefSource

# 配置日志
logger = logging.getLogger(__name__)

# 导入MCTS日志增强模块
try:
    from .mcts_logging import MCTSLogger, LogConfig
    MCTS_LOGGING_AVAILABLE = True
except ImportError:
    MCTS_LOGGING_AVAILABLE = False
    logger.warning("MCTS日志增强模块不可用，将使用基础日志功能")


class Node:
    """
    MCTS树节点

    表示搜索树中的一个节点，存储访问计数、值估计和先验概率等信息。
    支持存储信念状态相关信息，用于信念状态驱动的决策。
    """

    def __init__(self, prior: float = 0.0, player_id_to_act: str = None, use_value_distribution: bool = False, value_support_size: int = 601):
        """
        初始化MCTS节点

        Args:
            prior (float, optional): 从策略网络获得的先验概率. Defaults to 0.0.
            player_id_to_act (str, optional): 当前节点轮到行动的玩家ID. Defaults to None.
            use_value_distribution (bool, optional): 是否使用值分布. Defaults to False.
            value_support_size (int, optional): 值分布的支持大小. Defaults to 601.
        """
        # 节点统计信息
        self.visit_count = 0  # 访问次数

        # 根据是否使用值分布，初始化value_sum
        self.use_value_distribution = use_value_distribution
        if use_value_distribution:
            # 使用numpy数组存储值分布
            self.value_sum = np.zeros(value_support_size)
        else:
            # 使用标量存储累积值
            self.value_sum = 0.0

        self.prior = prior    # 先验概率
        self.children = {}    # 子节点：action -> Node

        # 当前节点轮到行动的玩家ID
        self.player_id_to_act = player_id_to_act

        # 模型预测的隐藏状态和奖励（仅MuZero使用）
        self.hidden_state = None
        self.reward = 0.0

        # 信念状态相关信息
        self.belief_confidence = 0.5  # 信念状态的置信度
        self.belief_entropy = 0.0     # 信念状态的熵（不确定性）
        self.belief_info_value = 0.0  # 信念状态的信息价值

        # 对手模型先验信息
        self.opponent_prior = 0.0     # 对手模型先验概率

        # 风险敏感决策相关信息
        self.risk_value = 0.0  # 风险敏感值
        self.cvar_value = 0.0  # CVaR值

        # 终止标志
        self.is_expanded = False

    def expanded(self) -> bool:
        """
        检查节点是否已展开

        Returns:
            bool: 如果节点已展开则为True，否则为False
        """
        return self.is_expanded

    def value(self) -> float:
        """
        获取节点的平均值

        Returns:
            float: 如果节点被访问过则返回平均值，否则返回0
        """
        if self.visit_count == 0:
            return 0.0

        # 处理数组类型的value_sum
        if isinstance(self.value_sum, np.ndarray) or isinstance(self.value_sum, torch.Tensor):
            if isinstance(self.value_sum, torch.Tensor):
                try:
                    # 检查tensor状态
                    if self.value_sum.numel() == 0:
                        # 空tensor，返回0
                        return 0.0

                    # 检查是否包含NaN或inf
                    if torch.isnan(self.value_sum).any() or torch.isinf(self.value_sum).any():
                        # 包含NaN或inf，使用0替代
                        import logging
                        logging.warning(f"Node value_sum包含NaN或inf值，使用0替代。tensor形状: {self.value_sum.shape}")
                        return 0.0

                    # 安全地计算mean
                    mean_value = self.value_sum.mean()
                    if torch.isnan(mean_value) or torch.isinf(mean_value):
                        import logging
                        logging.warning(f"计算mean后得到NaN或inf，使用0替代")
                        return 0.0

                    return mean_value.item() / self.visit_count
                except Exception as e:
                    # 如果tensor操作失败，记录错误并返回0
                    import logging
                    logging.error(f"处理torch.Tensor类型的value_sum时发生错误: {e}")
                    logging.error(f"tensor形状: {self.value_sum.shape if hasattr(self.value_sum, 'shape') else 'unknown'}")
                    logging.error(f"tensor数据类型: {self.value_sum.dtype if hasattr(self.value_sum, 'dtype') else 'unknown'}")
                    return 0.0
            else:
                try:
                    # 处理numpy数组
                    if self.value_sum.size == 0:
                        return 0.0

                    # 检查是否包含NaN或inf
                    if np.isnan(self.value_sum).any() or np.isinf(self.value_sum).any():
                        import logging
                        logging.warning(f"Node value_sum numpy数组包含NaN或inf值，使用0替代")
                        return 0.0

                    mean_value = np.mean(self.value_sum)
                    if np.isnan(mean_value) or np.isinf(mean_value):
                        import logging
                        logging.warning(f"numpy mean计算结果为NaN或inf，使用0替代")
                        return 0.0

                    return float(mean_value) / self.visit_count
                except Exception as e:
                    import logging
                    logging.error(f"处理numpy数组类型的value_sum时发生错误: {e}")
                    return 0.0

        # 处理标量类型
        try:
            if np.isnan(self.value_sum) or np.isinf(self.value_sum):
                import logging
                logging.warning(f"标量value_sum为NaN或inf，使用0替代")
                return 0.0
            return self.value_sum / self.visit_count
        except Exception as e:
            import logging
            logging.error(f"处理标量value_sum时发生错误: {e}")
            return 0.0

    def get_explanation_data(self) -> Dict[str, Any]:
        """
        获取节点的解释数据

        Returns:
            Dict[str, Any]: 包含节点统计信息的字典，用于解释决策过程
        """
        # 基本统计信息
        explanation_data = {
            'visit_count': self.visit_count,
            'value': self.value(),
            'prior': float(self.prior) if isinstance(self.prior, (np.ndarray, torch.Tensor)) else self.prior,
            'player_id': self.player_id_to_act
        }

        # 信念状态相关信息（如果有）
        if hasattr(self, 'belief_confidence') and self.belief_confidence != 0.5:
            explanation_data['belief_confidence'] = self.belief_confidence

        if hasattr(self, 'belief_entropy') and self.belief_entropy > 0:
            explanation_data['belief_entropy'] = self.belief_entropy

        if hasattr(self, 'belief_info_value') and self.belief_info_value > 0:
            explanation_data['info_value'] = self.belief_info_value

        # 对手模型先验信息（如果有）
        if hasattr(self, 'opponent_prior') and self.opponent_prior > 0:
            explanation_data['opponent_prior'] = self.opponent_prior

        # 风险敏感决策相关信息（如果有）
        if hasattr(self, 'risk_value') and self.risk_value != 0.0:
            explanation_data['risk_value'] = self.risk_value

        if hasattr(self, 'cvar_value') and self.cvar_value != 0.0:
            explanation_data['cvar_value'] = self.cvar_value

        # 奖励信息（如果有）
        if self.reward != 0.0:
            explanation_data['reward'] = self.reward

        return explanation_data


class MCTS:
    """
    蒙特卡洛树搜索

    实现标准的MCTS算法，可用于MuZero的规划阶段。
    """

    def __init__(
        self,
        num_simulations: int = 50,
        discount: float = 0.997,
        dirichlet_alpha: float = 0.25,
        exploration_fraction: float = 0.25,
        pb_c_base: int = 19652,
        pb_c_init: float = 1.25,
        root_exploration_noise: bool = True,
        use_belief_state: bool = False,
        use_information_value: bool = False,
        information_value_weight: float = 0.3,
        information_value_method: str = 'combined',
        use_intrinsic_motivation: bool = False,
        intrinsic_motivation_type: str = 'information_gain',
        intrinsic_motivation_weight: float = 0.5,
        use_opponent_model_prior: bool = False,  # 是否使用对手模型先验
        opponent_model_prior_weight: float = 0.5,  # 对手模型先验权重
        use_deep_belief_tracker: bool = False,  # 是否使用深度信念追踪器
        deep_belief_weight: float = 0.7,  # 深度信念权重，用于调整信念影响大小
        # 信念融合新参数
        select_belief_weight: float = 0.7,       # 选择阶段信念权重
        simulation_belief_weight: float = 0.5,   # 模拟阶段信念权重
        backprop_belief_weight: float = 0.3,     # 反向传播阶段信念权重
        use_belief_simulation: bool = False,     # 是否使用信念采样模拟
        belief_confidence_threshold: float = 0.3, # 信念置信度阈值，低于此值不应用信念调整
        belief_entropy_high: float = 0.7,        # 高熵阈值
        belief_entropy_low: float = 0.3,         # 低熵阈值
        exploration_bonus: float = 0.1,          # 高熵时的探索奖励
        exploitation_bonus: float = 0.05,        # 低熵时的利用奖励
        use_risk_sensitive_decision: bool = False,  # 是否使用风险敏感决策
        risk_alpha: float = 0.05,  # CVaR的置信水平
        risk_beta: float = 0.1,  # 风险厌恶系数
        use_act: bool = True,
        act_min_simulations: int = 10,
        act_confidence_threshold: float = 0.95,
        act_visit_threshold: int = 20,
        use_key_moment_detector: bool = False,
        key_moment_detector: Optional[Any] = None,
        key_moment_factor: float = 2.0,
        use_counterfactual: bool = False,
        spectral_clusters: int = 2
    ):
        """
        初始化MCTS算法

        Args:
            num_simulations (int, optional): 每次行动前的模拟次数. Defaults to 50.
            discount (float, optional): 折扣因子. Defaults to 0.997.
            dirichlet_alpha (float, optional): Dirichlet噪声的参数. Defaults to 0.25.
            exploration_fraction (float, optional): 根节点探索噪声的比例. Defaults to 0.25.
            pb_c_base (int, optional): PUCT公式的基础常数. Defaults to 19652.
            pb_c_init (float, optional): PUCT公式的初始常数. Defaults to 1.25.
            root_exploration_noise (bool, optional): 是否在根节点添加探索噪声. Defaults to True.
            use_belief_state (bool, optional): 是否使用信念状态. Defaults to False.
            use_information_value (bool, optional): 是否使用信息价值评估. Defaults to False.
            information_value_weight (float, optional): 信息价值权重. Defaults to 0.3.
            information_value_method (str, optional): 信息价值计算方法. Defaults to 'combined'.
            use_intrinsic_motivation (bool, optional): 是否使用内在动机. Defaults to False.
            intrinsic_motivation_type (str, optional): 内在动机类型. Defaults to 'information_gain'.
            intrinsic_motivation_weight (float, optional): 内在动机权重. Defaults to 0.5.
            use_opponent_model_prior (bool, optional): 是否使用对手模型先验. Defaults to False.
            opponent_model_prior_weight (float, optional): 对手模型先验权重. Defaults to 0.5.
            use_deep_belief_tracker (bool, optional): 是否使用深度信念追踪器. Defaults to False.
            deep_belief_weight (float, optional): 深度信念权重. Defaults to 0.7.
            select_belief_weight (float, optional): 选择阶段的信念权重. Defaults to 0.7.
            simulation_belief_weight (float, optional): 模拟阶段的信念权重. Defaults to 0.5.
            backprop_belief_weight (float, optional): 反向传播阶段的信念权重. Defaults to 0.3.
            use_belief_simulation (bool, optional): 是否使用信念采样进行模拟. Defaults to False.
            belief_confidence_threshold (float, optional): 信念置信度阈值，低于此值不应用信念调整. Defaults to 0.3.
            belief_entropy_high (float, optional): 高熵阈值，高于此值表示不确定性高. Defaults to 0.7.
            belief_entropy_low (float, optional): 低熵阈值，低于此值表示不确定性低. Defaults to 0.3.
            exploration_bonus (float, optional): 高熵(高不确定性)时的探索奖励. Defaults to 0.1.
            exploitation_bonus (float, optional): 低熵(低不确定性)时的利用奖励. Defaults to 0.05.
            use_risk_sensitive_decision (bool, optional): 是否使用风险敏感决策. Defaults to False.
            risk_alpha (float, optional): CVaR的置信水平. Defaults to 0.05.
            risk_beta (float, optional): 风险厌恶系数. Defaults to 0.1.
            use_act (bool, optional): 是否使用自适应计算时间. Defaults to True.
            act_min_simulations (int, optional): ACT最小模拟次数. Defaults to 10.
            act_confidence_threshold (float, optional): ACT置信度阈值. Defaults to 0.95.
            act_visit_threshold (int, optional): ACT访问次数阈值. Defaults to 20.
            use_key_moment_detector (bool, optional): 是否使用关键时刻检测器. Defaults to False.
            key_moment_detector (Optional[Any], optional): 关键时刻检测器实例. Defaults to None.
            key_moment_factor (float, optional): 关键时刻因子，用于调整计算预算. Defaults to 2.0.
            use_counterfactual (bool, optional): 是否使用反事实推理. Defaults to False.
            spectral_clusters (int, optional): 谱聚类的簇数. Defaults to 2.
        """
        # 搜索参数
        self.num_simulations = num_simulations
        self.discount = discount
        self.dirichlet_alpha = dirichlet_alpha
        self.exploration_fraction = exploration_fraction
        self.pb_c_base = pb_c_base
        self.pb_c_init = pb_c_init
        self.root_exploration_noise = root_exploration_noise

        # 信念状态相关参数
        self.use_belief_state = use_belief_state
        self.use_information_value = use_information_value
        self.information_value_weight = information_value_weight
        self.information_value_method = information_value_method

        # 内在动机相关参数
        self.use_intrinsic_motivation = use_intrinsic_motivation
        self.intrinsic_motivation_type = intrinsic_motivation_type
        self.intrinsic_motivation_weight = intrinsic_motivation_weight

        # 对手模型相关参数
        self.use_opponent_model_prior = use_opponent_model_prior
        self.opponent_model_prior_weight = opponent_model_prior_weight

        # 深度信念追踪器相关参数
        self.use_deep_belief_tracker = use_deep_belief_tracker
        self.deep_belief_weight = deep_belief_weight

        # 信念融合新参数
        self.select_belief_weight = select_belief_weight
        self.simulation_belief_weight = simulation_belief_weight
        self.backprop_belief_weight = backprop_belief_weight
        self.use_belief_simulation = use_belief_simulation
        self.belief_confidence_threshold = belief_confidence_threshold
        self.belief_entropy_high = belief_entropy_high
        self.belief_entropy_low = belief_entropy_low
        self.exploration_bonus = exploration_bonus
        self.exploitation_bonus = exploitation_bonus

        # 风险敏感决策相关参数
        self.use_risk_sensitive_decision = use_risk_sensitive_decision
        self.risk_alpha = risk_alpha
        self.risk_beta = risk_beta

        # ACT相关参数
        self.use_act = use_act
        self.act_min_simulations = act_min_simulations
        self.act_confidence_threshold = act_confidence_threshold
        self.act_visit_threshold = act_visit_threshold

        # 关键时刻检测器相关参数
        self.use_key_moment_detector = use_key_moment_detector
        self.key_moment_detector = key_moment_detector
        self.key_moment_factor = key_moment_factor

        # 反事实推理相关参数
        self.use_counterfactual = use_counterfactual
        self.spectral_clusters = spectral_clusters

        # 统计信息
        self.actual_simulations = 0

        # 初始化MCTS日志器
        self.mcts_logger = None
        if MCTS_LOGGING_AVAILABLE:
            try:
                # 创建默认日志配置
                log_config = LogConfig(
                    enabled=True,
                    level="DEBUG",
                    output_format="json",
                    enable_ucb_logging=True,
                    enable_expansion_logging=True,
                    enable_path_logging=True,
                    enable_performance_logging=True,
                    log_to_file=True,
                    log_to_console=False
                )
                self.mcts_logger = MCTSLogger(config=log_config)
                logger.info(f"MCTS日志器初始化成功，会话ID: {self.mcts_logger.session_id}")
            except Exception as e:
                logger.warning(f"MCTS日志器初始化失败: {e}")
                self.mcts_logger = None

    def run(
        self,
        root_state: Any,
        model: Any,
        temperature: float = 1.0,
        actions_mask: Optional[List[int]] = None,
        belief_trackers: Optional[Dict[str, Any]] = None,
        opponent_model_priors: Optional[Dict[str, Dict[int, float]]] = None,  # 对手模型先验
        deepbelief_tracker: Optional[Dict[str, Any]] = None,  # 深度信念追踪器
        explain: bool = False,
        max_time_ms: Optional[int] = None,
        use_act: Optional[bool] = None,
        dynamic_budget: Optional[Dict[str, Any]] = None,  # 动态计算预算
        force_exploration: bool = False  # 是否强制进行探索
    ) -> Union[Tuple[Dict[int, int], Dict[int, float]], Tuple[Dict[int, int], Dict[int, float], Dict[str, Any]]]:
        """
        运行MCTS搜索

        Args:
            root_state (Any): 根状态，可以是游戏状态或表示网络输出的隐藏状态
            model (Any): 用于预测的模型（表示、动态和预测网络）
            temperature (float, optional): 温度参数，用于控制探索. Defaults to 1.0.
            actions_mask (Optional[List[int]], optional): 合法动作掩码. Defaults to None.
            belief_trackers (Optional[Dict[str, Any]], optional): 信念追踪器字典，键为玩家ID. Defaults to None.
            opponent_model_priors (Optional[Dict[str, Dict[int, float]]], optional): 对手模型先验，键为玩家ID，值为动作到概率的映射. Defaults to None.
            deepbelief_tracker (Optional[Dict[str, Any]], optional): 深度信念追踪器字典，键为玩家ID，用于提供更精确的信念信息. Defaults to None.
            explain (bool, optional): 是否返回解释数据. Defaults to False.
            max_time_ms (Optional[int], optional): 最大搜索时间（毫秒）. Defaults to None.
            use_act (Optional[bool], optional): 是否使用自适应计算时间，覆盖初始化时的设置. Defaults to None.
            dynamic_budget (Optional[Dict[str, Any]], optional): 动态计算预算配置. Defaults to None.

        Returns:
            Union[Tuple[Dict[int, int], Dict[int, float]], Tuple[Dict[int, int], Dict[int, float], Dict[str, Any]]]:
                如果explain=False，返回访问计数和搜索策略；
                如果explain=True，返回访问计数、搜索策略和解释数据
        """
        # 记录开始时间，用于计算搜索时间
        start_time_ms = int(time.time() * 1000)

        # 清除之前的状态
        self.actual_simulations = 0

        # 开始MCTS搜索计时
        search_id = None
        if self.mcts_logger:
            search_id = self.mcts_logger.start_search_timing()

        # 决定是否使用ACT
        use_act_for_this_run = self.use_act if use_act is None else use_act

        # 确定当前玩家ID（如果提供）
        current_player_id = None
        if hasattr(root_state, 'current_player'):
            current_player_id = getattr(root_state, 'current_player')
        elif hasattr(root_state, 'to_play'):
            current_player_id = getattr(root_state, 'to_play')

        # 获取信念状态（如果可用）
        belief_state = None
        if self.use_belief_state and belief_trackers and current_player_id:
            # 优先使用深度信念追踪器（如果提供）
            if deepbelief_tracker and current_player_id in deepbelief_tracker:
                belief_state = deepbelief_tracker[current_player_id].get_belief_state()
            # 回退到普通信念追踪器
            elif current_player_id in belief_trackers:
                belief_state = belief_trackers[current_player_id].get_belief_state()

        # 检查模型是否支持分布式价值头
        use_value_distribution = False
        value_support_size = 601  # 默认值

        # 检查模型是否有分布式价值头属性
        if hasattr(model, 'use_distributional_value') and model.use_distributional_value:
            use_value_distribution = True
            if hasattr(model, 'value_support_size'):
                value_support_size = model.value_support_size

        # 创建根节点，传递分布式价值头相关参数
        root = Node(
            prior=0,
            player_id_to_act=current_player_id,
            use_value_distribution=use_value_distribution,
            value_support_size=value_support_size
        )

        # 使用表示网络获取初始隐藏状态
        root.hidden_state = model.represent(root_state)
        root.reward = 0.0  # 根节点没有奖励

        # 扩展根节点
        # 如果模型支持信念状态输入，则传入信念状态
        if self.use_belief_state and belief_state and hasattr(model, 'predict_with_belief'):
            # 使用支持信念状态的预测方法
            policy_logits, value = model.predict_with_belief(root.hidden_state, belief_state)
        else:
            # 使用标准预测方法
            policy_logits, value = model.predict(root.hidden_state)

        # 处理对手模型先验
        # 如果启用了对手模型先验，且提供了当前玩家的先验信息，则使用它
        current_opponent_model_priors = None
        if self.use_opponent_model_prior and opponent_model_priors and current_player_id is not None:
            # 获取当前玩家的对手模型先验
            current_opponent_model_priors = opponent_model_priors.get(current_player_id)

            # 如果没有当前玩家的对手模型先验，但有全局对手模型先验，则使用全局对手模型先验
            if current_opponent_model_priors is None and "global" in opponent_model_priors:
                current_opponent_model_priors = opponent_model_priors.get("global")

            if current_opponent_model_priors:
                # 记录日志
                if hasattr(self, 'logger') and self.logger:
                    self.logger.debug(f"使用对手模型先验: {current_player_id}, {len(current_opponent_model_priors)} 个动作")

        # 扩展根节点
        self._expand_node(root, actions_mask, policy_logits, belief_trackers, current_opponent_model_priors)

        # 添加探索噪声
        if self.root_exploration_noise:
            self._add_exploration_noise(root, actions_mask)

        # 动态调整模拟次数 (如果启用了关键时刻检测器)
        num_simulations = self.num_simulations
        if self.use_key_moment_detector and self.key_moment_detector and hasattr(self.key_moment_detector, 'is_key_moment'):
            is_key = self.key_moment_detector.is_key_moment(root_state)
            if is_key:
                num_simulations = int(num_simulations * self.key_moment_factor)
                logger.debug(f"检测到关键时刻，增加模拟次数到 {num_simulations}")

        # 如果提供了动态预算配置，使用它来调整模拟次数
        if dynamic_budget:
            budget_factor = dynamic_budget.get('budget_factor', 1.0)
            num_simulations = int(num_simulations * budget_factor)
            logger.debug(f"使用动态预算因子 {budget_factor}，调整模拟次数到 {num_simulations}")

        # 主循环
        search_paths = []
        for _ in range(num_simulations):
            # 检查搜索是否应该提前终止
            if use_act_for_this_run and self.actual_simulations >= self.act_min_simulations:
                if self._check_act_termination(root):
                    break

            # 检查是否超时
            if max_time_ms is not None and int(time.time() * 1000) - start_time_ms > max_time_ms:
                logger.debug(f"MCTS搜索超时，完成 {self.actual_simulations} 次模拟")
                break

            # 克隆根状态
            state = root_state.clone() if hasattr(root_state, 'clone') else root_state

            # 搜索
            search_path = [root]
            current_node = root

            # 选择阶段：从根节点到叶节点
            while current_node.expanded():
                action, current_node = self._select_child(current_node, belief_trackers)
                search_path.append(current_node)

                # 如果游戏具有应用动作的方法，则应用动作
                if hasattr(state, 'apply_action'):
                    state = state.apply_action(action)

            # 存储搜索路径
            search_paths.append(search_path)

            # 扩展阶段：扩展叶节点
            # 如果节点对应终止状态，则不扩展
            if not state.is_terminal() if hasattr(state, 'is_terminal') else True:
                # 递归调用表示和预测网络
                # 如果模型支持信念状态输入，则传入信念状态
                if self.use_belief_state and belief_state and hasattr(model, 'predict_with_belief'):
                    # 使用支持信念状态的预测方法
                    current_hidden = model.represent(state)
                    current_node.hidden_state = current_hidden
                    policy_logits, value = model.predict_with_belief(current_hidden, belief_state)
                else:
                    # 使用标准预测方法
                    current_hidden = model.represent(state)
                    current_node.hidden_state = current_hidden
                    policy_logits, value = model.predict(current_hidden)

                # 扩展节点
                self._expand_node(current_node, actions_mask, policy_logits, belief_trackers, current_opponent_model_priors)

                # 信念模拟阶段：使用信念采样进行模拟
                use_belief_sim = self.use_belief_simulation and random.random() < self.simulation_belief_weight and self.use_belief_state and belief_trackers
                if use_belief_sim:
                    # 使用信念采样进行模拟
                    value = self._belief_simulation(state, belief_trackers)
            else:
                # 游戏结束，使用实际奖励
                value = state.get_reward() if hasattr(state, 'get_reward') else 0.0

            # 反向传播阶段：更新统计信息
            self._backpropagate(search_path, value, self.discount, belief_trackers)

            # 更新模拟计数
            self.actual_simulations += 1

        # 计算访问计数和搜索策略
        visit_counts = {}
        for action, child in root.children.items():
            if child.visit_count > 0:
                visit_counts[action] = child.visit_count

        # 如果没有访问计数，随机选择一个合法动作
        if not visit_counts:
            legal_actions = []
            for action, child in root.children.items():
                # 如果有动作掩码，检查动作是否合法
                if actions_mask is None or actions_mask[action] == 1:
                    legal_actions.append(action)

            if legal_actions:
                # 随机选择一个合法动作
                selected_action = random.choice(legal_actions)
                visit_counts[selected_action] = 1
            else:
                # 如果没有合法动作，随机选择一个动作
                selected_action = random.choice(list(root.children.keys()))
                visit_counts[selected_action] = 1

        # 计算搜索策略
        # 如果强制探索，使用更高的温度
        effective_temperature = temperature
        if force_exploration:
            effective_temperature = max(temperature, 2.0)  # 确保温度至少为2.0，增加随机性

        policy = {
            action: (count / sum(visit_counts.values())) ** (1 / effective_temperature)
            for action, count in visit_counts.items()
        }

        # 归一化
        sum_policy = sum(policy.values())
        policy = {action: prob / sum_policy for action, prob in policy.items()}

        # 结束MCTS搜索计时并记录性能日志
        if self.mcts_logger and search_id:
            search_time = self.mcts_logger.end_search_timing(search_id, self.actual_simulations)
            # 记录性能统计
            self.mcts_logger.log_performance_stats({
                'search_time': search_time,
                'num_simulations_planned': num_simulations,
                'num_simulations_actual': self.actual_simulations,
                'search_efficiency': self.actual_simulations / num_simulations if num_simulations > 0 else 0,
                'root_visit_count': root.visit_count,
                'num_actions_explored': len(root.children),
                'policy_entropy': self._calculate_decision_entropy(policy)
            })

        # 返回结果
        if explain:
            explanation = self._generate_explanation(root, visit_counts, policy)

            # 保存信念状态信息到解释中（如果可用）
            if self.use_belief_state and belief_state:
                explanation['belief_state'] = {
                    'confidence': getattr(belief_state, 'confidence', 0.5),
                    'entropy': getattr(belief_state, 'entropy', 0.0),
                    'source': str(getattr(belief_state, 'source', 'unknown'))
                }

                # 如果可用，保存TopK牌概率
                card_probs = getattr(belief_state, 'card_probabilities', {})
                if card_probs:
                    # 获取TopK牌概率
                    top_k = 10  # 最多保存前10张牌
                    top_cards = sorted(card_probs.items(), key=lambda x: x[1], reverse=True)[:top_k]
                    explanation['belief_state']['top_cards'] = {card: prob for card, prob in top_cards}

            # 保存当前玩家ID
            if current_player_id:
                explanation['current_player_id'] = current_player_id

            # 保存模拟次数
            explanation['actual_simulations'] = self.actual_simulations
            explanation['max_simulations'] = num_simulations

            # 保存搜索路径信息
            if search_paths:
                explanation['avg_search_path_length'] = sum(len(path) for path in search_paths) / len(search_paths)
                explanation['max_search_path_length'] = max(len(path) for path in search_paths)

            return visit_counts, policy, explanation
        else:
            return visit_counts, policy

    def _generate_explanation(self, root: Node, visit_counts: Dict[int, int], policy: Dict[int, float]) -> Dict[str, Any]:
        """
        生成MCTS搜索的解释数据

        Args:
            root (Node): 根节点
            visit_counts (Dict[int, int]): 访问计数
            policy (Dict[int, float]): 搜索策略

        Returns:
            Dict[str, Any]: 解释数据
        """
        # 基本解释数据
        explanation = {
            'root_info': root.get_explanation_data(),
            'total_simulations': self.num_simulations,
            'actual_simulations': self.actual_simulations,
            'search_policy': policy,
            'visit_counts': visit_counts,
            'top_actions': [],
            'principal_variation': [],
            'decision_metrics': {
                'exploration_exploitation_ratio': self._calculate_exploration_exploitation_ratio(root),
                'decision_entropy': self._calculate_decision_entropy(policy),
                'search_depth_distribution': self._calculate_search_depth_distribution(root),
                'value_confidence': self._calculate_value_confidence(root)
            }
        }

        # 添加ACT相关信息
        if self.use_act:
            # 计算ACT置信度
            if len(visit_counts) >= 2:
                sorted_visits = sorted(visit_counts.values(), reverse=True)
                best_visits = sorted_visits[0]
                second_best_visits = sorted_visits[1]
                total_visits = root.visit_count
                act_confidence = (best_visits - second_best_visits) / total_visits if total_visits > 0 else 0

                # 添加ACT信息
                explanation['act_info'] = {
                    'enabled': True,
                    'confidence': act_confidence,
                    'confidence_threshold': self.act_confidence_threshold,
                    'min_simulations': self.act_min_simulations,
                    'visit_threshold': self.act_visit_threshold,
                    'early_stopped': self.actual_simulations < self.num_simulations
                }
            else:
                explanation['act_info'] = {
                    'enabled': True,
                    'early_stopped': False
                }
        else:
            explanation['act_info'] = {
                'enabled': False
            }

        # 添加动态计算预算相关信息
        explanation['budget_info'] = {
            'base_simulations': getattr(self, 'original_num_simulations', self.num_simulations),
            'actual_simulations': self.actual_simulations,
            'allocated_simulations': self.num_simulations,
            'dynamic_budget': self.num_simulations != getattr(self, 'original_num_simulations', self.num_simulations)
        }

        # 添加内在动机相关信息
        if self.use_intrinsic_motivation and self.intrinsic_motivation:
            explanation['intrinsic_motivation_info'] = {
                'enabled': True,
                'type': self.intrinsic_motivation_type,
                'weight': self.intrinsic_motivation_weight
            }

            # 如果是组合内在动机，添加组件信息
            if self.intrinsic_motivation_type == 'composite' and hasattr(self.intrinsic_motivation, 'motivations'):
                components = []
                for i, motivation in enumerate(self.intrinsic_motivation.motivations):
                    component_info = {
                        'type': motivation.__class__.__name__,
                        'weight': self.intrinsic_motivation.weights[i] if hasattr(self.intrinsic_motivation, 'weights') else motivation.weight
                    }
                    components.append(component_info)
                explanation['intrinsic_motivation_info']['components'] = components

            # 添加每个动作的内在奖励信息
            action_intrinsic_bonuses = {}
            for action, child in root.children.items():
                if hasattr(child, 'intrinsic_bonus') and child.intrinsic_bonus > 0:
                    action_intrinsic_bonuses[action] = child.intrinsic_bonus

            if action_intrinsic_bonuses:
                explanation['intrinsic_motivation_info']['action_bonuses'] = action_intrinsic_bonuses
        else:
            explanation['intrinsic_motivation_info'] = {
                'enabled': False
            }

        # 添加对手模型先验相关信息
        if self.use_opponent_model_prior:
            explanation['opponent_model_prior_info'] = {
                'enabled': True,
                'weight': self.opponent_model_prior_weight
            }
        else:
            explanation['opponent_model_prior_info'] = {
                'enabled': False
            }

        # 添加风险敏感决策相关信息
        if self.use_risk_sensitive_decision:
            explanation['risk_sensitive_decision_info'] = {
                'enabled': True,
                'alpha': self.risk_alpha,
                'beta': self.risk_beta
            }

            # 如果根节点有风险敏感值，添加到解释中
            if hasattr(root, 'risk_value'):
                explanation['risk_sensitive_decision_info']['root_risk_value'] = root.risk_value

            # 如果根节点有CVaR值，添加到解释中
            if hasattr(root, 'cvar_value'):
                explanation['risk_sensitive_decision_info']['root_cvar_value'] = root.cvar_value
        else:
            explanation['risk_sensitive_decision_info'] = {
                'enabled': False
            }

        # 获取访问次数最多的前3个动作
        sorted_actions = sorted(visit_counts.items(), key=lambda x: x[1], reverse=True)
        top_n = min(3, len(sorted_actions))

        # 添加顶级动作信息
        for i in range(top_n):
            action, count = sorted_actions[i]
            child = root.children[action]

            action_info = {
                'action': action,
                'visit_count': count,
                'visit_percentage': count / self.num_simulations,
                'value': child.value(),
                'prior': float(child.prior) if isinstance(child.prior, (np.ndarray, torch.Tensor)) else child.prior,
                'policy_prob': policy[action]
            }

            # 添加信念状态相关信息（如果有）
            if hasattr(child, 'belief_info_value') and child.belief_info_value > 0:
                action_info['info_value'] = child.belief_info_value

            # 添加对手模型先验信息（如果有）
            if hasattr(child, 'opponent_prior') and child.opponent_prior > 0:
                action_info['opponent_prior'] = child.opponent_prior

            # 添加风险敏感决策相关信息（如果有）
            if self.use_risk_sensitive_decision:
                if hasattr(child, 'risk_value') and child.risk_value != 0.0:
                    action_info['risk_value'] = child.risk_value

                if hasattr(child, 'cvar_value') and child.cvar_value != 0.0:
                    action_info['cvar_value'] = child.cvar_value

            # 添加内在动机相关信息（如果有）
            if self.use_intrinsic_motivation and self.intrinsic_motivation:
                # 获取信念状态
                belief_state = None
                if self.use_belief_state and hasattr(root, 'player_id_to_act'):
                    player_id = root.player_id_to_act
                    if player_id:
                        # 从解释数据中获取信念状态
                        belief_trackers = explanation.get('belief_trackers', {})
                        if player_id in belief_trackers:
                            belief_tracker = belief_trackers[player_id]
                            belief_state = getattr(belief_tracker, 'get_belief_state', lambda: None)()

                # 计算内在奖励
                if belief_state:
                    intrinsic_bonus = self.intrinsic_motivation.compute_bonus(
                        state=root.hidden_state,
                        action=action,
                        belief_state=belief_state,
                        policy=np.array([c.prior for _, c in root.children.items()]) if root.children else None
                    )

                    # 添加到动作信息
                    if intrinsic_bonus > 0:
                        action_info['intrinsic_bonus'] = intrinsic_bonus

            explanation['top_actions'].append(action_info)

        # 生成主要变化路径（从根节点开始，每次选择访问次数最多的子节点）
        current_node = root
        max_depth = 5  # 增加最大深度限制，提供更详细的搜索路径
        current_depth = 0

        while current_node.children and current_depth < max_depth:
            # 找到访问次数最多的子节点
            max_visits = -1
            best_action = None
            best_child = None

            for action, child in current_node.children.items():
                if child.visit_count > max_visits:
                    max_visits = child.visit_count
                    best_action = action
                    best_child = child

            if best_action is None or best_child is None:
                break

            # 添加到主要变化路径
            variation_step = {
                'action': best_action,
                'node_info': best_child.get_explanation_data(),
                'depth': current_depth + 1,
                'visit_percentage': best_child.visit_count / root.visit_count if root.visit_count > 0 else 0
            }
            explanation['principal_variation'].append(variation_step)

            # 移动到下一个节点
            current_node = best_child
            current_depth += 1

        # 添加搜索树统计信息
        explanation['tree_stats'] = {
            'max_depth': max(node.get('depth', 0) for node in explanation['principal_variation']) if explanation['principal_variation'] else 0,
            'branching_factor': len(root.children),
            'total_nodes': sum(1 for _ in self._count_nodes(root)),
            'leaf_nodes': sum(1 for _ in self._count_leaf_nodes(root)),
            'avg_depth': self._calculate_avg_depth(root)
        }

        # 添加搜索树可视化数据
        explanation['tree_visualization'] = {
            'root': self._generate_tree_visualization_data(root, max_depth=3)
        }

        # 添加反事实推理结果
        if self.use_counterfactual and hasattr(root, 'counterfactual_values'):
            explanation['counterfactual_values'] = root.counterfactual_values

        return explanation

    def _count_nodes(self, node: Node):
        """计算节点及其所有子节点的数量"""
        yield node
        for child in node.children.values():
            yield from self._count_nodes(child)

    def _count_leaf_nodes(self, node: Node):
        """计算叶节点的数量"""
        if not node.children:
            yield node
        else:
            for child in node.children.values():
                yield from self._count_leaf_nodes(child)

    def _calculate_avg_depth(self, node: Node, depth: int = 0) -> float:
        """计算平均深度"""
        if not node.children:
            return depth

        depths = [self._calculate_avg_depth(child, depth + 1) for child in node.children.values()]
        return sum(depths) / len(depths) if depths else depth

    def _calculate_exploration_exploitation_ratio(self, root: Node) -> float:
        """
        计算探索与利用的比率

        通过分析访问次数分布来评估搜索的探索与利用平衡

        Args:
            root: 根节点

        Returns:
            float: 探索与利用比率，值越高表示探索性越强，值越低表示利用性越强
        """
        if not root.children:
            return 0.0

        # 获取所有子节点的访问次数
        visits = [child.visit_count for child in root.children.values()]

        # 如果总访问次数为0，返回默认值
        total_visits = sum(visits)
        if total_visits == 0:
            return 0.5

        # 计算访问次数的标准差
        mean_visits = total_visits / len(visits)
        variance = sum((v - mean_visits) ** 2 for v in visits) / len(visits)
        std_dev = variance ** 0.5

        # 计算变异系数（标准差/均值），用于衡量分布的不均匀程度
        cv = std_dev / mean_visits if mean_visits > 0 else 0

        # 将变异系数映射到[0, 1]范围，值越低表示探索性越强
        # 使用sigmoid函数进行映射
        import math
        exploration_ratio = 1.0 / (1.0 + math.exp(5 * (cv - 0.5)))

        return exploration_ratio

    def _calculate_decision_entropy(self, policy: Dict[int, float]) -> float:
        """
        计算决策熵

        评估决策的不确定性程度

        Args:
            policy: 搜索策略（动作到概率的映射）

        Returns:
            float: 决策熵，值越高表示不确定性越大
        """
        if not policy:
            return 0.0

        import math

        # 计算熵: -sum(p * log(p))
        entropy = -sum(p * math.log(p) if p > 0 else 0 for p in policy.values())

        # 归一化熵到[0, 1]范围
        max_entropy = math.log(len(policy)) if len(policy) > 0 else 1.0
        normalized_entropy = entropy / max_entropy if max_entropy > 0 else 0.0

        return normalized_entropy

    def _calculate_search_depth_distribution(self, root: Node) -> Dict[str, Any]:
        """
        计算搜索深度分布

        分析搜索树的深度分布情况

        Args:
            root: 根节点

        Returns:
            Dict[str, Any]: 搜索深度分布信息
        """
        # 收集所有叶节点的深度
        leaf_depths = []
        self._collect_leaf_depths(root, 0, leaf_depths)

        if not leaf_depths:
            return {
                'min_depth': 0,
                'max_depth': 0,
                'avg_depth': 0.0,
                'median_depth': 0,
                'depth_histogram': {}
            }

        # 计算深度统计信息
        min_depth = min(leaf_depths)
        max_depth = max(leaf_depths)
        avg_depth = sum(leaf_depths) / len(leaf_depths)

        # 计算中位数深度
        sorted_depths = sorted(leaf_depths)
        n = len(sorted_depths)
        if n % 2 == 0:
            median_depth = (sorted_depths[n//2 - 1] + sorted_depths[n//2]) / 2
        else:
            median_depth = sorted_depths[n//2]

        # 生成深度直方图
        depth_histogram = {}
        for depth in leaf_depths:
            depth_histogram[depth] = depth_histogram.get(depth, 0) + 1

        # 将直方图转换为百分比
        total_leaves = len(leaf_depths)
        depth_histogram = {d: count / total_leaves for d, count in depth_histogram.items()}

        return {
            'min_depth': min_depth,
            'max_depth': max_depth,
            'avg_depth': avg_depth,
            'median_depth': median_depth,
            'depth_histogram': depth_histogram
        }

    def _collect_leaf_depths(self, node: Node, current_depth: int, depths: List[int]):
        """
        收集所有叶节点的深度

        Args:
            node: 当前节点
            current_depth: 当前深度
            depths: 用于存储深度的列表
        """
        if not node.children:
            depths.append(current_depth)
            return

        for child in node.children.values():
            self._collect_leaf_depths(child, current_depth + 1, depths)

    def _calculate_value_confidence(self, root: Node) -> float:
        """
        计算值估计的置信度

        基于根节点子节点的值估计分布评估置信度

        Args:
            root: 根节点

        Returns:
            float: 值估计置信度，值越高表示置信度越高
        """
        if not root.children:
            return 0.0

        # 获取所有子节点的值估计
        values = [child.value() for child in root.children.values()]

        # 如果没有有效的值估计，返回默认值
        if not values:
            return 0.5

        # 计算值估计的标准差
        mean_value = sum(values) / len(values)
        variance = sum((v - mean_value) ** 2 for v in values) / len(values)
        std_dev = variance ** 0.5

        # 计算变异系数（标准差/均值的绝对值）
        abs_mean = abs(mean_value) if abs(mean_value) > 1e-6 else 1e-6
        cv = std_dev / abs_mean

        # 将变异系数映射到[0, 1]范围的置信度，变异系数越小，置信度越高
        confidence = max(0.0, min(1.0, 1.0 - min(cv, 1.0)))

        return confidence

    def _generate_tree_visualization_data(self, node: Node, max_depth: int = 3, current_depth: int = 0) -> Dict[str, Any]:
        """
        生成用于可视化的树结构数据

        Args:
            node: 当前节点
            max_depth: 最大深度限制
            current_depth: 当前深度

        Returns:
            Dict[str, Any]: 树结构数据
        """
        if current_depth > max_depth or not node.children:
            return {
                'visit_count': node.visit_count,
                'value': node.value(),
                'prior': float(node.prior) if isinstance(node.prior, (np.ndarray, torch.Tensor)) else node.prior,
                'reward': float(node.reward) if node.reward != 0.0 else 0.0,
                'depth': current_depth,
                'is_leaf': not node.children,
                'children': []
            }

        children_data = []
        for action, child in node.children.items():
            # 只包含访问次数较多的子节点，避免数据过大
            if child.visit_count > node.visit_count * 0.03:  # 降低阈值至3%，包含更多子节点
                # 计算UCB得分
                pb_c = math.log((node.visit_count + self.pb_c_base + 1) / self.pb_c_base) + self.pb_c_init
                pb_c *= math.sqrt(node.visit_count) / (child.visit_count + 1)
                prior = float(child.prior) if isinstance(child.prior, (np.ndarray, torch.Tensor)) else child.prior
                ucb_score = child.value() + prior * pb_c

                child_data = {
                    'action': action,
                    'visit_count': child.visit_count,
                    'value': child.value(),
                    'prior': float(child.prior) if isinstance(child.prior, (np.ndarray, torch.Tensor)) else child.prior,
                    'reward': float(child.reward) if child.reward != 0.0 else 0.0,
                    'ucb_score': float(ucb_score),
                    'visit_percentage': child.visit_count / node.visit_count if node.visit_count > 0 else 0,
                    'depth': current_depth + 1,
                    'is_leaf': not child.children,
                    'children': self._generate_tree_visualization_data(child, max_depth, current_depth + 1)['children']
                }

                # 添加信念状态相关信息（如果有）
                if hasattr(child, 'belief_confidence') and child.belief_confidence != 0.5:
                    child_data['belief_confidence'] = float(child.belief_confidence)

                if hasattr(child, 'belief_entropy') and child.belief_entropy > 0:
                    child_data['belief_entropy'] = float(child.belief_entropy)

                if hasattr(child, 'belief_info_value') and child.belief_info_value > 0:
                    child_data['info_value'] = float(child.belief_info_value)

                # 添加对手模型先验信息（如果有）
                if hasattr(child, 'opponent_prior') and child.opponent_prior > 0:
                    child_data['opponent_prior'] = float(child.opponent_prior)

                # 添加风险敏感决策相关信息（如果有）
                if hasattr(child, 'risk_value') and child.risk_value != 0.0:
                    child_data['risk_value'] = float(child.risk_value)

                if hasattr(child, 'cvar_value') and child.cvar_value != 0.0:
                    child_data['cvar_value'] = float(child.cvar_value)

                # 添加内在动机相关信息（如果有）
                if hasattr(child, 'intrinsic_bonus') and child.intrinsic_bonus > 0:
                    child_data['intrinsic_bonus'] = float(child.intrinsic_bonus)

                children_data.append(child_data)

        # 按访问次数排序
        children_data.sort(key=lambda x: x['visit_count'], reverse=True)

        # 限制子节点数量，避免数据过大
        max_children = 7  # 增加显示的子节点数量
        if len(children_data) > max_children:
            children_data = children_data[:max_children]

        # 计算子节点的统计信息
        if children_data:
            child_values = [child['value'] for child in children_data]
            child_visits = [child['visit_count'] for child in children_data]

            children_stats = {
                'count': len(children_data),
                'total_children': len(node.children),
                'avg_value': sum(child_values) / len(child_values),
                'min_value': min(child_values),
                'max_value': max(child_values),
                'value_std': (sum((v - sum(child_values) / len(child_values)) ** 2 for v in child_values) / len(child_values)) ** 0.5,
                'visit_concentration': max(child_visits) / sum(child_visits) if sum(child_visits) > 0 else 0
            }
        else:
            children_stats = {
                'count': 0,
                'total_children': len(node.children),
                'avg_value': 0,
                'min_value': 0,
                'max_value': 0,
                'value_std': 0,
                'visit_concentration': 0
            }

        return {
            'visit_count': node.visit_count,
            'value': node.value(),
            'prior': float(node.prior) if isinstance(node.prior, (np.ndarray, torch.Tensor)) else node.prior,
            'reward': float(node.reward) if node.reward != 0.0 else 0.0,
            'depth': current_depth,
            'is_leaf': not node.children,
            'children_stats': children_stats,
            'children': children_data
        }

    def _select_child(self, node: Node, belief_trackers: Optional[Dict[str, Any]] = None) -> Tuple[int, Node]:
        """
        使用PUCT公式选择子节点，并考虑信念状态

        Args:
            node (Node): 当前节点
            belief_trackers (Optional[Dict[str, Any]], optional): 信念追踪器字典，键为玩家ID. Defaults to None.

        Returns:
            Tuple[int, Node]: 选择的动作和对应的子节点
        """
        max_ucb = float('-inf')
        max_action = -1
        max_child = None

        # 开始UCB计算计时
        ucb_timing_id = None
        if self.mcts_logger:
            ucb_timing_id = self.mcts_logger.start_ucb_timing()

        # 用于记录所有子节点的UCB分数
        children_scores = []

        # 获取信念状态（如果可用）
        belief_state = None
        if self.use_belief_state and belief_trackers and node.player_id_to_act:
            # 获取当前玩家的信念追踪器
            belief_tracker = belief_trackers.get(node.player_id_to_act)
            if belief_tracker:
                # 获取信念状态
                belief_state = belief_tracker.get_belief_state()

        # 判断信念状态的有效性和置信度
        valid_belief = belief_state and getattr(belief_state, 'confidence', 0) >= self.belief_confidence_threshold
        # 判断是否是深度信念追踪器
        is_deep_belief = valid_belief and getattr(belief_state, 'source', None) == BeliefSource.NEURAL
        deep_belief_confidence = getattr(belief_state, 'confidence', 0.5) if valid_belief else 0.5

        # 获取玩家ID(为了区分自己和对手)
        player_id = node.player_id_to_act

        # 基础UCB公式构成部分
        total_visits_sqrt = math.sqrt(node.visit_count) if node.visit_count > 0 else 1.0

        # UCB得分 = Q(s,a) + P(s,a) * sqrt(sum_b(N(s,b))) / (1 + N(s,a))
        for action, child in node.children.items():
            # 探索项
            pb_c = math.log((node.visit_count + self.pb_c_base + 1) / self.pb_c_base) + self.pb_c_init
            pb_c *= total_visits_sqrt / (child.visit_count + 1)

            # 处理数组类型的prior
            prior = child.prior
            if isinstance(prior, np.ndarray) or isinstance(prior, torch.Tensor):
                if isinstance(prior, torch.Tensor):
                    prior = prior.mean().item()
                else:
                    prior = float(np.mean(prior))

            # 获取节点值
            value = child.value()

            # 如果启用了风险敏感决策，调整节点值
            if self.use_risk_sensitive_decision and hasattr(self, 'cvar_calculator'):
                # 如果节点有分布式价值，计算风险调整的值
                if hasattr(child, 'use_value_distribution') and child.use_value_distribution and hasattr(child, 'value_distribution'):
                    value_dist = child.value_distribution
                    risk_value = self.cvar_calculator.calculate_cvar(value_dist, self.risk_alpha)
                    # 根据风险厌恶系数混合风险调整的值和原始值
                    value = (1 - self.risk_beta) * value + self.risk_beta * risk_value

            # ------------ 增强的信念状态融合 (选择阶段) ------------
            if valid_belief:
                # 1. 获取信念状态的牌概率分布和置信度
                card_probs = getattr(belief_state, 'card_probabilities', {})
                confidence = getattr(belief_state, 'confidence', 0.5)

                # 2. 基于手牌概率分布计算动作的信念奖励
                belief_bonus = 0.0
                action_expected_value = 0.0
                if card_probs:
                    # 获取动作与牌的映射关系
                    action_card_mapping = self._get_action_card_mapping(action)
                    if action_card_mapping:
                        # 增强型：计算牌型在当前信念下的期望值
                        weighted_sum = 0.0
                        weight_sum = 0.0
                        for card, weight in action_card_mapping.items():
                            if card in card_probs:
                                card_prob = card_probs[card]
                                weighted_sum += card_prob * weight
                                weight_sum += weight

                        if weight_sum > 0:
                            # 归一化加权和
                            action_expected_value = weighted_sum / weight_sum

                            # 权重越高的牌出现概率越高，奖励越大
                            # 引入非线性变换，使高概率的变化更显著
                            belief_bonus = math.pow(action_expected_value, 2) * 2

                # 3. 应用信念奖励 (根据选择阶段信念权重和置信度)
                if belief_bonus > 0:
                    # 使用更强的权重，确保信念能显著影响UCB
                    belief_factor = self.select_belief_weight * confidence
                    # 直接影响UCB加权
                    value += belief_bonus * belief_factor

                # 4. 深度信念追踪器特殊处理
                if is_deep_belief and self.use_deep_belief_tracker:
                    # 增强型：使用sigmoid函数使高置信度的深度信念有更强影响
                    if deep_belief_confidence > 0.6:
                        sigmoid_conf = 1.0 / (1.0 + math.exp(-(deep_belief_confidence - 0.75) * 10))
                        deep_belief_factor = self.deep_belief_weight * sigmoid_conf
                        if belief_bonus > 0:
                            # 非线性增强高置信度时的影响
                            value += belief_bonus * deep_belief_factor * 1.5

                # 5. 基于信念熵调整探索-利用平衡
                entropy = self._calculate_belief_entropy(card_probs) if card_probs else 0.5
                if entropy > self.belief_entropy_high:  # 高不确定性
                    # 高熵时增加探索量，使用非线性映射
                    exploration_boost = self.exploration_bonus * (1 + math.tanh(entropy * 2 - 1))
                    pb_c *= (1 + exploration_boost)
                elif entropy < self.belief_entropy_low:  # 低不确定性
                    # 低熵时增加利用权重，使用非线性映射
                    exploitation_boost = self.exploitation_bonus * (1 + math.tanh(1 - entropy * 2))
                    value += exploitation_boost

                # 6. 考虑动作可能揭示的信息价值，新增更全面评估
                if self.use_information_value:
                    info_value = self._estimate_action_info_value(action, belief_state)
                    if info_value > 0:
                        # 根据节点访问次数动态调整信息价值权重
                        # 访问次数少时给予更高权重，鼓励探索信息丰富的路径
                        visit_ratio = math.sqrt(1.0 / (child.visit_count + 1))
                        adaptive_weight = self.information_value_weight * (1 + visit_ratio)
                        value += adaptive_weight * info_value

            # 如果启用了对手模型先验，考虑对手模型先验
            if self.use_opponent_model_prior and hasattr(child, 'opponent_prior') and child.opponent_prior > 0:
                # 根据对手模型先验调整UCB得分
                opponent_bonus = self.opponent_model_prior_weight * child.opponent_prior
                value += opponent_bonus

            # 如果启用了内在动机，考虑内在奖励
            if self.use_intrinsic_motivation and hasattr(child, 'intrinsic_bonus') and child.intrinsic_bonus > 0:
                # 将内在奖励添加到值估计中
                intrinsic_bonus = child.intrinsic_bonus * self.intrinsic_motivation_weight
                value += intrinsic_bonus

            # 基础UCB得分
            ucb_score = value + prior * pb_c

            # 记录子节点UCB分数信息
            child_score_info = {
                'action': action,
                'visits': child.visit_count,
                'value': float(value),
                'prior': float(prior),
                'pb_c': float(pb_c),
                'ucb_score': float(ucb_score)
            }

            # 添加信念状态相关信息（如果有）
            if valid_belief:
                child_score_info['belief_confidence'] = getattr(belief_state, 'confidence', 0.5)
                if hasattr(child, 'belief_info_value'):
                    child_score_info['belief_info_value'] = float(child.belief_info_value)

            children_scores.append(child_score_info)

            # 更新最大值
            if ucb_score > max_ucb:
                max_ucb = ucb_score
                max_action = action
                max_child = child

        # 记录UCB计算日志
        if self.mcts_logger and children_scores:
            game_context = {
                'player_id': node.player_id_to_act,
                'node_visits': node.visit_count,
                'num_children': len(node.children),
                'belief_state_available': valid_belief,
                'selected_action': max_action,
                'max_ucb_score': float(max_ucb)
            }

            self.mcts_logger.log_ucb_calculation(
                parent_node=node,
                children_scores=children_scores,
                selected_action=max_action,
                game_context=game_context,
                timing_id=ucb_timing_id
            )

        return max_action, max_child

    def _calculate_belief_entropy(self, card_probabilities: Dict[str, float]) -> float:
        """
        计算信念状态的熵（不确定性度量）

        Args:
            card_probabilities (Dict[str, float]): 牌的概率分布

        Returns:
            float: 熵值，范围在[0, 1]
        """
        # 过滤掉概率为0的牌
        probs = [p for p in card_probabilities.values() if p > 0]

        if not probs:
            return 0.0

        # 归一化概率
        sum_probs = sum(probs)
        if sum_probs > 0:
            probs = [p / sum_probs for p in probs]

        # 计算熵: -sum(p * log(p))
        entropy = -sum(p * math.log(p) if p > 0 else 0 for p in probs)

        # 归一化熵到[0, 1]范围
        max_entropy = math.log(len(probs)) if len(probs) > 0 else 1.0
        normalized_entropy = entropy / max_entropy if max_entropy > 0 else 0.0

        return normalized_entropy

    def _estimate_action_info_value(self, action: int, belief_state: Any) -> float:
        """
        估计动作的信息价值（动作可能揭示的信息量）

        Args:
            action (int): 动作ID
            belief_state (Any): 信念状态

        Returns:
            float: 信息价值，范围在[0, 1]
        """
        # 如果启用了信息价值评估，则使用更高级的评估方法
        if self.use_information_value and hasattr(belief_state, 'card_probabilities'):
            # 获取所有牌
            all_cards = list(belief_state.card_probabilities.keys())

            # 如果没有牌，则返回0
            if not all_cards:
                return 0.0

            # 选择一些代表性的牌进行评估（为了效率）
            # 在实际应用中，可以根据动作特性选择相关的牌
            top_cards = []
            for card, prob in belief_state.card_probabilities.items():
                # 只考虑概率在0.1到0.9之间的牌（不确定的牌）
                if 0.1 < prob < 0.9:
                    top_cards.append(card)
                    if len(top_cards) >= 5:  # 最多评估5张牌
                        break

            # 如果没有不确定的牌，则使用简化方法
            if not top_cards:
                return self._simple_info_value_estimate(action, belief_state)

            # 计算这些牌的平均信息价值
            total_info_value = 0.0
            for card in top_cards:
                # 使用信息价值计算模块计算信息价值
                card_info_value = calculate_information_value(
                    belief_state,
                    card,
                    method=self.information_value_method
                )
                total_info_value += card_info_value

            # 计算平均信息价值
            avg_info_value = total_info_value / len(top_cards) if top_cards else 0.0

            # 根据动作特性调整信息价值
            # 这里假设动作ID与牌有某种映射关系
            # 实际应用中，应该基于游戏规则和动作特性进行更精确的估计
            action_factor = self._get_action_info_factor(action, belief_state)

            # 最终信息价值
            return avg_info_value * action_factor
        else:
            # 使用简化的方法估计信息价值
            return self._simple_info_value_estimate(action, belief_state)

    def _simple_info_value_estimate(self, action: int, belief_state: Any) -> float:
        """
        使用简化方法估计动作的信息价值

        Args:
            action (int): 动作ID
            belief_state (Any): 信念状态

        Returns:
            float: 信息价值，范围在[0, 1]
        """
        # 获取信念状态的不确定性
        card_probs = getattr(belief_state, 'card_probabilities', {})
        if not card_probs:
            return 0.0

        # 计算当前的平均不确定性
        current_uncertainty = self._calculate_belief_entropy(card_probs)

        # 获取动作的信息潜力
        action_info_potential = self._get_action_info_factor(action, belief_state)

        # 信息价值与当前不确定性和动作潜力相关
        info_value = current_uncertainty * action_info_potential

        return info_value

    def _get_action_info_factor(self, action: int, belief_state: Optional[Any] = None) -> float:
        """
        获取动作的信息潜力因子

        Args:
            action (int): 动作ID
            belief_state (Optional[Any], optional): 信念状态. Defaults to None.

        Returns:
            float: 信息潜力因子，范围在[0, 1]
        """
        # 如果启用了内在动机，并且内在动机是基于信息增益的，使用其方法
        if (self.use_intrinsic_motivation and self.intrinsic_motivation and
            hasattr(self.intrinsic_motivation, '_get_action_info_factor') and
            belief_state is not None):
            try:
                # 尝试使用内在动机的方法
                return self.intrinsic_motivation._get_action_info_factor(action, belief_state)
            except Exception as e:
                # 如果出错，回退到默认方法
                import logging
                logging.warning(f"使用内在动机的_get_action_info_factor方法失败: {e}")

        # 默认方法：简单假设动作ID越大，可能揭示的信息越多
        # 实际应用中，应该基于游戏规则和动作特性进行更精确的估计
        # 例如，出牌动作比过牌动作更可能揭示信息

        # 根据动作类型估计信息潜力
        action_type = action % 10  # 简单地取动作ID的个位数作为动作类型

        if action_type == 0:  # 假设0代表"不出"
            return 0.1  # "不出"通常揭示较少信息
        elif action_type == 1:  # 假设1代表"出单张"
            return 1.0  # 出单张通常揭示较多信息
        elif action_type == 2:  # 假设2代表"出对子"
            return 0.8  # 出对子揭示中等信息
        else:
            # 其他情况，使用简化的映射
            return min(1.0, action / 100.0)

    def _expand_node(self, node: Node, actions_mask: Optional[List[int]], policy_logits: List[float],
                  belief_trackers: Optional[Dict[str, Any]] = None,
                  opponent_model_priors: Optional[Dict[str, Dict[int, float]]] = None):
        """
        展开节点，添加子节点，并根据信念状态和对手模型先验调整先验概率

        Args:
            node (Node): 要展开的节点
            actions_mask (Optional[List[int]]): 合法动作掩码
            policy_logits (List[float]): 策略网络的输出
            belief_trackers (Optional[Dict[str, Any]], optional): 信念追踪器字典，键为玩家ID. Defaults to None.
            opponent_model_priors (Optional[Dict[str, Dict[int, float]]], optional): 对手模型先验，键为玩家ID，值为动作到概率的映射. Defaults to None.
        """
        # 开始节点扩展计时
        expansion_timing_id = None
        expansion_start_time = time.perf_counter()
        if self.mcts_logger:
            expansion_timing_id = self.mcts_logger.start_expansion_timing()
        # 计算策略
        # 处理torch.Tensor类型的policy_logits
        if isinstance(policy_logits, torch.Tensor):
            # 将张量转换为numpy数组
            policy_logits_np = policy_logits.detach().cpu().numpy()

            # 如果是二维数组，取第一行
            if len(policy_logits_np.shape) > 1:
                policy_logits_np = policy_logits_np[0]

            # 应用动作掩码
            if actions_mask is not None:
                assert len(policy_logits_np) == len(actions_mask), "策略输出维度与动作掩码不一致"
                policy_logits_np = [p if mask else float('-inf') for p, mask in zip(policy_logits_np, actions_mask)]

            # 计算策略
            max_logit = np.max(policy_logits_np)
            policy = np.exp(policy_logits_np - max_logit)
            policy = policy / np.sum(policy)
        else:
            # 处理numpy数组或列表
            # 如果是二维数组，取第一行
            if isinstance(policy_logits, np.ndarray) and len(policy_logits.shape) > 1:
                policy_logits = policy_logits[0]

            # 应用动作掩码
            if actions_mask is not None:
                assert len(policy_logits) == len(actions_mask), "策略输出维度与动作掩码不一致"
                policy_logits = [p if mask else float('-inf') for p, mask in zip(policy_logits, actions_mask)]

            max_logit = np.max(policy_logits)
            policy = np.exp(policy_logits - max_logit)
            policy = policy / np.sum(policy)

        # 获取信念状态（如果可用）
        belief_state = None
        if self.use_belief_state and belief_trackers and node.player_id_to_act:
            # 获取当前玩家的信念追踪器
            belief_tracker = belief_trackers.get(node.player_id_to_act)
            if belief_tracker:
                # 获取信念状态
                belief_state = belief_tracker.get_belief_state()

        # 获取深度信念追踪器（如果可用）
        deep_belief_network = None
        if self.use_deep_belief_tracker and belief_trackers and node.player_id_to_act:
            deep_belief_tracker = belief_trackers.get(node.player_id_to_act)
            if hasattr(deep_belief_tracker, 'network') and deep_belief_tracker.network is not None:
                deep_belief_network = deep_belief_tracker.network

        # 检查是否有联合信念状态
        joint_belief_state = None
        if belief_trackers and hasattr(belief_trackers, 'get_joint_belief_state'):
            # 一些信念追踪器可能提供联合信念状态
            joint_belief_state = belief_trackers.get_joint_belief_state()

        # 获取对手模型先验（如果可用）
        opponent_prior = None
        if self.use_opponent_model_prior and opponent_model_priors and node.player_id_to_act:
            # 获取当前玩家的对手模型先验
            opponent_prior = opponent_model_priors.get(node.player_id_to_act)

        # 添加子节点
        for action, prob in enumerate(policy):
            if actions_mask is None or actions_mask[action]:
                # 初始先验概率
                adjusted_prob = prob

                # 如果有深度信念网络和信念状态，使用更先进的方法调整先验
                if deep_belief_network and belief_state:
                    try:
                        # 使用深度信念网络推断动作在当前信念状态下的价值
                        belief_action_logits = deep_belief_network.predict_action_likelihood(
                            action=action,
                            belief_state=belief_state,
                            player_id=node.player_id_to_act
                        )

                        # 将模型先验和信念推断结合，使用自适应权重融合
                        deep_belief_weight = self.deep_belief_weight
                        # 调整权重：高置信度时增大深度信念的影响
                        confidence = getattr(belief_state, 'confidence', 0.5)
                        if confidence > 0.7:
                            deep_belief_weight *= 1.5

                        # 使用加权平均融合先验和深度信念
                        adjusted_prob = (1 - deep_belief_weight) * adjusted_prob + deep_belief_weight * belief_action_logits
                    except (AttributeError, Exception) as e:
                        # 如果深度信念网络推断失败，回退到传统方法
                        logger.debug(f"深度信念网络推断失败: {e}，回退到传统方法")
                        # 回退到基础调整
                        if belief_state:
                            adjusted_prob = self._adjust_prior_with_belief(action, adjusted_prob, belief_state)
                else:
                    # 如果有信念状态，使用传统方法调整先验概率
                    if belief_state:
                        # 根据信念状态调整先验概率
                        adjusted_prob = self._adjust_prior_with_belief(action, adjusted_prob, belief_state)

                # 如果有联合信念状态，进一步优化先验
                if joint_belief_state and hasattr(joint_belief_state, 'get_joint_probability'):
                    try:
                        # 获取与当前动作相关的牌型映射
                        action_card_mapping = self._get_action_card_mapping(action)

                        # 如果存在映射关系，使用联合信念状态计算联合概率
                        if action_card_mapping:
                            # 准备牌分配假设
                            card_assignments = {}
                            joint_prob = 0.0

                            # 遍历牌和权重
                            for card, weight in action_card_mapping.items():
                                # 创建一个简单的分配假设：当前玩家持有该牌
                                card_assignments[node.player_id_to_act] = card

                                # 计算这种分配的联合概率
                                assignment_prob = joint_belief_state.get_joint_probability(card_assignments)

                                # 根据权重累加联合概率
                                joint_prob += assignment_prob * weight

                            # 如果联合概率有效，融合到先验中
                            if joint_prob > 0:
                                # 使用信念状态置信度作为联合信念的权重
                                joint_belief_weight = getattr(belief_state, 'confidence', 0.5) * 0.5  # 控制影响力度

                                # 融合联合信念
                                adjusted_prob = (1 - joint_belief_weight) * adjusted_prob + joint_belief_weight * joint_prob
                    except Exception as e:
                        # 联合信念状态计算失败，记录但继续处理
                        logger.debug(f"联合信念概率计算失败: {e}")

                # 如果有对手模型先验，则进一步调整先验概率
                if opponent_prior and action in opponent_prior and self.use_opponent_model_prior:
                    # 获取对手模型对该动作的先验概率
                    opponent_prob = opponent_prior[action]

                    # 融合模型先验和对手模型先验
                    # 使用加权平均的方式融合
                    adjusted_prob = (1 - self.opponent_model_prior_weight) * adjusted_prob + \
                                   self.opponent_model_prior_weight * opponent_prob

                # 创建子节点，并传递玩家ID和分布式价值头相关参数
                child_node = Node(
                    prior=adjusted_prob,
                    player_id_to_act=node.player_id_to_act,
                    use_value_distribution=getattr(node, 'use_value_distribution', False),
                    value_support_size=getattr(node, 'value_sum', np.array([])).shape[0] if hasattr(node, 'use_value_distribution') and node.use_value_distribution else 601
                )

                # 存储信念状态相关信息（如果有）
                if belief_state:
                    # 这可以帮助在后续搜索中更好地利用信念信息
                    child_node.belief_confidence = getattr(belief_state, 'confidence', 0.5)

                    # 计算并存储信念状态的熵和信息价值
                    if hasattr(belief_state, 'card_probabilities'):
                        card_probs = belief_state.card_probabilities
                        child_node.belief_entropy = self._calculate_belief_entropy(card_probs)

                        # 如果启用了信息价值评估，则计算并存储信息价值
                        if self.use_information_value:
                            # 估计动作的信息价值
                            child_node.belief_info_value = self._estimate_action_info_value(action, belief_state)

                    # 存储信念来源信息
                    belief_source = getattr(belief_state, 'source', None)
                    if belief_source:
                        child_node.belief_source = belief_source

                # 存储对手模型先验信息（如果有）
                if opponent_prior and action in opponent_prior:
                    child_node.opponent_prior = opponent_prior[action]

                # 如果启用了内在动机，计算并存储内在奖励
                if self.use_intrinsic_motivation and self.intrinsic_motivation:
                    try:
                        # 准备参数
                        kwargs = {
                            'belief_state': belief_state,
                            'policy': policy
                        }

                        # 计算内在奖励
                        intrinsic_bonus = self.intrinsic_motivation.compute_bonus(
                            state=node.hidden_state,
                            action=action,
                            **kwargs
                        )

                        # 存储内在奖励
                        if intrinsic_bonus > 0:
                            child_node.intrinsic_bonus = intrinsic_bonus
                    except Exception as e:
                        logger.warning(f"在展开节点时计算内在奖励失败: {e}")

                # 添加子节点
                node.children[action] = child_node

        node.is_expanded = True

        # 记录节点扩展日志
        if self.mcts_logger:
            expansion_time = time.perf_counter() - expansion_start_time
            num_children = len(node.children)

            # 准备策略输出信息
            policy_output = {
                'policy_logits_shape': len(policy_logits) if hasattr(policy_logits, '__len__') else 'unknown',
                'actions_mask_provided': actions_mask is not None,
                'num_legal_actions': sum(actions_mask) if actions_mask else 'unknown',
                'belief_trackers_available': belief_trackers is not None,
                'opponent_priors_available': opponent_model_priors is not None
            }

            # 如果有策略分布信息，添加到输出中
            if 'policy' in locals():
                policy_output['policy_entropy'] = self._calculate_decision_entropy(
                    {i: p for i, p in enumerate(policy) if p > 0}
                )
                policy_output['max_policy_prob'] = float(np.max(policy))
                policy_output['min_policy_prob'] = float(np.min(policy[policy > 0])) if np.any(policy > 0) else 0.0

            self.mcts_logger.log_node_expansion(
                node=node,
                policy_output=policy_output,
                expansion_time=expansion_time,
                num_children=num_children,
                timing_id=expansion_timing_id
            )

        # 反事实推理: 生成根节点的反事实分支价值
        if self.use_counterfactual:
            counterfactual_values = {}
            for action, child in root.children.items():
                # 模拟单步动态转移
                cf_hidden_state, cf_reward = model.dynamics(root.hidden_state, action)
                # 预测反事实价值
                if self.use_belief_state and belief_state and hasattr(model, 'predict_with_belief'):
                    _, cf_value = model.predict_with_belief(cf_hidden_state, belief_state)
                else:
                    _, cf_value = model.predict(cf_hidden_state)
                counterfactual_values[action] = cf_value
            # 存储到根节点
            setattr(root, 'counterfactual_values', counterfactual_values)

    def _check_act_termination(self, root: Node) -> bool:
        """
        检查是否满足ACT终止条件

        根据访问次数和置信度判断是否可以提前终止搜索。

        Args:
            root (Node): 根节点

        Returns:
            bool: 如果满足终止条件则返回True，否则返回False
        """
        # 如果没有启用ACT，直接返回False
        if not self.use_act:
            return False

        # 如果模拟次数少于最小阈值，不终止
        if self.actual_simulations < self.act_min_simulations:
            return False

        # 如果根节点访问次数少于阈值，不终止
        if root.visit_count < self.act_visit_threshold:
            return False

        # 获取访问次数最多的前两个动作
        visit_counts = [(action, child.visit_count) for action, child in root.children.items()]
        if len(visit_counts) < 2:
            return False

        # 按访问次数排序
        visit_counts.sort(key=lambda x: x[1], reverse=True)
        best_action_visits = visit_counts[0][1]
        second_best_action_visits = visit_counts[1][1]

        # 计算总访问次数
        total_visits = root.visit_count

        # 计算置信度
        # 使用最佳动作和次佳动作的访问次数差异来衡量置信度
        confidence = (best_action_visits - second_best_action_visits) / total_visits if total_visits > 0 else 0

        # 如果置信度超过阈值，可以提前终止
        if confidence > self.act_confidence_threshold:
            return True

        # 检查最佳动作的值估计是否稳定
        best_action = visit_counts[0][0]
        best_child = root.children[best_action]

        # 如果最佳动作的值估计非常高（接近1.0），可以提前终止
        if best_child.value() > 0.95:
            return True

        return False

    def _add_exploration_noise(self, node: Node, actions_mask: Optional[List[int]] = None):
        """
        在根节点添加Dirichlet噪声以促进探索

        Args:
            node (Node): 根节点
            actions_mask (Optional[List[int]], optional): 合法动作掩码，用于过滤非法动作. Defaults to None.
        """
        # 获取所有动作
        actions = list(node.children.keys())

        # 如果没有动作，直接返回
        if not actions:
            return

        # 如果提供了动作掩码，过滤非法动作
        if actions_mask is not None:
            legal_actions = [i for i, mask in enumerate(actions_mask) if mask]
            actions = [action for action in actions if action in legal_actions]

            # 如果没有合法动作，直接返回
            if not actions:
                return

        # 生成Dirichlet噪声
        noise = np.random.dirichlet([self.dirichlet_alpha] * len(actions))

        # 将噪声添加到先验概率
        for i, action in enumerate(actions):
            node.children[action].prior = (
                (1 - self.exploration_fraction) * node.children[action].prior
                + self.exploration_fraction * noise[i]
            )

    def _adjust_prior_with_belief(self, action: int, prior: float, belief_state: Any) -> float:
        """
        根据信念状态调整先验概率

        Args:
            action (int): 动作ID
            prior (float): 原始先验概率
            belief_state (Any): 信念状态

        Returns:
            float: 调整后的先验概率
        """
        # 获取信念状态的置信度
        confidence = getattr(belief_state, 'confidence', 0.5)

        # 获取信念状态中的牌概率分布
        card_probs = getattr(belief_state, 'card_probabilities', {})

        # 判断是否来自深度信念追踪器
        is_deep_belief = getattr(belief_state, 'source', None) == BeliefSource.NEURAL

        # 计算信念状态的熵（不确定性）
        entropy = 0.0
        if card_probs:
            entropy = self._calculate_belief_entropy(card_probs)

        # 估计动作的信息价值
        info_value = self._estimate_action_info_value(action, belief_state)

        # 调整先验概率的策略：
        # 1. 基于置信度调整
        confidence_factor = 1.0
        if confidence > 0.7:
            # 高置信度时增强先验概率
            confidence_factor = 1.0 + 0.3 * (confidence - 0.5)
        elif confidence < 0.3:
            # 低置信度时降低先验概率
            confidence_factor = 1.0 - 0.3 * (0.5 - confidence)

        # 2. 基于熵（不确定性）调整
        entropy_factor = 1.0
        if entropy > 0.7:
            # 高不确定性时，增加探索倾向
            entropy_factor = 1.0 + 0.2 * entropy
        elif entropy < 0.3:
            # 低不确定性时，增强利用倾向
            entropy_factor = 1.0 + 0.1 * (1.0 - entropy)

        # 3. 基于信息价值调整
        info_value_factor = 1.0 + 0.2 * info_value

        # 4. 深度信念追踪器特殊调整
        deep_belief_factor = 1.0
        if is_deep_belief and self.use_deep_belief_tracker:
            # 深度信念时，根据置信度进一步增强调整
            deep_belief_factor = 1.0 + self.deep_belief_weight * (confidence - 0.5)

            # 如果深度信念追踪器指示某些牌型的高概率
            # 可以进一步强化相关动作的先验概率
            # 这里假设动作ID与牌型有某种对应关系，需要根据具体游戏调整
            action_card_mapping = self._get_action_card_mapping(action)
            if action_card_mapping:
                card_belief_boost = 0.0
                for card, weight in action_card_mapping.items():
                    if card in card_probs:
                        card_belief_boost += card_probs[card] * weight

                # 应用牌型信念提升
                if card_belief_boost > 0:
                    deep_belief_factor *= (1.0 + card_belief_boost)

        # 综合调整因子
        adjustment_factor = confidence_factor * entropy_factor * info_value_factor * deep_belief_factor

        # 应用调整
        adjusted_prior = prior * adjustment_factor

        # 确保先验概率在有效范围内
        return max(0.001, min(0.999, adjusted_prior))

    def _get_action_card_mapping(self, action: int) -> Dict[Any, float]:
        """
        获取动作与牌的映射关系，用于信念状态评估

        Args:
            action (int): 要评估的动作

        Returns:
            Dict[Any, float]: 牌到权重的映射字典
        """
        # 如果动作是整数ID，尝试获取动作与牌的映射
        action_card_mapping = {}

        # 检查是否有动作描述器
        if hasattr(self, 'action_descriptor') and self.action_descriptor:
            # 尝试获取动作描述
            action_desc = self.action_descriptor.get_action_description(action)
            if action_desc and isinstance(action_desc, dict):
                # 从动作描述中提取牌信息
                if 'cards' in action_desc:
                    cards = action_desc['cards']
                    if isinstance(cards, (list, tuple, set)):
                        for card in cards:
                            action_card_mapping[card] = 1.0
                    elif isinstance(cards, dict):
                        action_card_mapping.update(cards)

                # 如果有特定的权重信息
                if 'card_weights' in action_desc:
                    action_card_mapping.update(action_desc['card_weights'])

        # 如果没有获取到映射，尝试其他方法
        if not action_card_mapping:
            # 检查是否有动作解码器
            if hasattr(self, 'action_decoder') and self.action_decoder:
                try:
                    # 尝试解码动作为牌集合
                    decoded_cards = self.action_decoder.decode_action(action)
                    if decoded_cards:
                        if isinstance(decoded_cards, (list, tuple, set)):
                            for card in decoded_cards:
                                action_card_mapping[card] = 1.0
                        elif isinstance(decoded_cards, dict):
                            action_card_mapping.update(decoded_cards)
                except:
                    pass

            # 检查是否支持游戏特定的映射方法
            if not action_card_mapping and hasattr(self, 'game_specific_mapping'):
                try:
                    mapping = self.game_specific_mapping(action)
                    if mapping:
                        action_card_mapping.update(mapping)
                except:
                    pass

        return action_card_mapping

    def _calculate_decision_entropy(self, policy: Dict[int, float]) -> float:
        """
        计算决策策略的熵值

        Args:
            policy: 动作概率分布字典

        Returns:
            float: 熵值
        """
        if not policy:
            return 0.0

        entropy = 0.0
        for prob in policy.values():
            if prob > 0:
                entropy -= prob * math.log2(prob)

        return entropy

    def _backpropagate(self, search_path: List[Node], value: Union[float, torch.Tensor, np.ndarray], discount: float, belief_trackers: Optional[Dict[str, Any]] = None) -> float:
        """
        反向传播更新节点统计信息，考虑信念状态的不确定性和信息价值

        Args:
            search_path (List[Node]): 搜索路径中的节点
            value (Union[float, torch.Tensor, np.ndarray]): 初始值估计，可以是标量或分布
            discount (float): 折扣因子
            belief_trackers (Optional[Dict[str, Any]], optional): 信念追踪器字典，键为玩家ID. Defaults to None.

        Returns:
            float: 根节点的更新值
        """
        # 记录搜索路径日志
        if self.mcts_logger and search_path:
            try:
                if isinstance(value, (torch.Tensor, np.ndarray)):
                    if isinstance(value, torch.Tensor):
                        # 安全地处理torch.Tensor
                        if value.numel() == 0:
                            path_value = 0.0
                        elif torch.isnan(value).any() or torch.isinf(value).any():
                            path_value = 0.0
                        else:
                            path_value = float(value.mean().item())
                    else:
                        # 安全地处理numpy数组
                        if value.size == 0:
                            path_value = 0.0
                        elif np.isnan(value).any() or np.isinf(value).any():
                            path_value = 0.0
                        else:
                            path_value = float(np.mean(value))
                else:
                    # 处理标量值
                    if np.isnan(value) or np.isinf(value):
                        path_value = 0.0
                    else:
                        path_value = float(value)

                self.mcts_logger.log_search_path(
                    path=search_path,
                    path_value=path_value,
                    depth=len(search_path)
                )
            except Exception as e:
                logger.warning(f"记录搜索路径日志时发生错误: {e}")
                # 继续执行，不因为日志记录失败而中断

        # 计算路径中的总信息价值（如果启用）
        total_info_value = 0.0
        if self.use_information_value:
            # 从叶节点到根节点，计算每个节点的信息价值
            for i, _ in enumerate(search_path):
                if i < len(search_path) - 1:  # 跳过根节点
                    # 获取下一个节点
                    next_node = search_path[i + 1]

                    # 如果有信念状态相关信息，则计算信息价值
                    if hasattr(next_node, 'belief_info_value') and next_node.belief_info_value > 0:
                        total_info_value += next_node.belief_info_value

        # 检查是否有联合信念状态
        joint_belief_state = None
        if belief_trackers and hasattr(belief_trackers, 'get_joint_belief_state'):
            # 一些信念追踪器可能提供联合信念状态
            joint_belief_state = belief_trackers.get_joint_belief_state()

        # 检查值是否为分布
        is_distribution = isinstance(value, (torch.Tensor, np.ndarray))

        # 如果是分布，确保它是numpy数组
        if is_distribution:
            if isinstance(value, torch.Tensor):
                value_distribution = value.detach().cpu().numpy()
            else:
                value_distribution = value

            # 计算标量值（用于传统更新）
            scalar_value = float(np.mean(value_distribution))
        else:
            # 如果是标量，直接使用
            scalar_value = value

        # 计算路径的期望价值（考虑信念状态），用于整体调整
        expected_path_value = 0.0
        valid_belief_count = 0

        # 计算路径上所有节点的信念加权平均值估计
        for node in search_path:
            if hasattr(node, 'player_id_to_act') and node.player_id_to_act and belief_trackers:
                belief_tracker = belief_trackers.get(node.player_id_to_act)
                if belief_tracker:
                    belief_state = belief_tracker.get_belief_state()
                    if belief_state and getattr(belief_state, 'confidence', 0) >= self.belief_confidence_threshold:
                        # 如果有多个有效的信念状态，计算平均期望值
                        expected_path_value += node.value() * getattr(belief_state, 'confidence', 0.5)
                        valid_belief_count += 1

        # 如果有有效的信念状态，计算期望路径值
        path_belief_factor = 1.0
        if valid_belief_count > 0:
            expected_path_value /= valid_belief_count

            # 根据期望路径值和初始值的差异调整因子
            if abs(expected_path_value) > 0.01:  # 避免除以接近零的值
                # 如果期望值和初始值方向一致，增强；反之，减弱
                value_ratio = scalar_value / expected_path_value if expected_path_value != 0 else 1.0
                if value_ratio > 0:  # 方向一致
                    path_belief_factor = 1.1  # 适度增强
                else:  # 方向不一致
                    path_belief_factor = 0.9  # 适度减弱

        # 反向传播
        for node in reversed(search_path):
            # ------------ 增强的信念状态融合 (反向传播阶段) ------------
            # 基础更新权重
            update_weight = 1.0 * path_belief_factor  # 应用路径信念因子

            # 获取信念状态（如果可用）
            belief_state = None
            if self.use_belief_state and belief_trackers and node.player_id_to_act:
                # 获取当前玩家的信念追踪器
                belief_tracker = belief_trackers.get(node.player_id_to_act)
                if belief_tracker:
                    # 获取信念状态
                    belief_state = belief_tracker.get_belief_state()

            # 如果信念状态有效且置信度高于阈值
            if belief_state and getattr(belief_state, 'confidence', 0) >= self.belief_confidence_threshold:
                # 获取信念状态的置信度和熵
                confidence = getattr(belief_state, 'confidence', 0.5)
                card_probs = getattr(belief_state, 'card_probabilities', {})
                entropy = self._calculate_belief_entropy(card_probs) if card_probs else 0.5

                # 1. 基于置信度非线性调整更新权重
                # 使用sigmoid函数使高置信度的影响更强
                if confidence > 0.5:
                    conf_factor = 2.0 / (1.0 + math.exp(-10 * (confidence - 0.7))) - 0.5
                    update_weight += self.backprop_belief_weight * conf_factor
                else:
                    # 低置信度减弱权重
                    conf_factor = (confidence - 0.5) * 2  # 映射到[-1, 0]范围
                    update_weight += self.backprop_belief_weight * conf_factor * 0.5  # 减弱影响

                # 2. 低熵状态(高确定性)给予更高权重，高熵状态给予更低权重
                if entropy < self.belief_entropy_low:
                    # 低熵时使用非线性增强
                    entropy_boost = 1.0 + (1.0 - entropy/self.belief_entropy_low) * 0.3
                    update_weight *= entropy_boost
                elif entropy > self.belief_entropy_high:
                    # 高熵时减弱影响
                    entropy_penalty = 1.0 - (entropy - self.belief_entropy_high) * 0.2
                    update_weight *= max(0.7, entropy_penalty)  # 确保权重不会过低

                # 3. 深度信念追踪器特殊处理
                if hasattr(belief_state, 'source') and getattr(belief_state, 'source', None) == BeliefSource.NEURAL:
                    # 从深度信念模型来的信念状态权重更高
                    deep_belief_confidence = getattr(belief_state, 'confidence', 0.5)
                    if deep_belief_confidence > 0.6:  # 高置信度
                        # 使用ReLU-like激活增强高置信度的影响
                        deep_factor = max(0, (deep_belief_confidence - 0.6)) * 3.0
                        update_weight *= (1.0 + self.deep_belief_weight * deep_factor)

                # 4. 如果有联合信念状态，结合联合概率进一步优化更新
                if joint_belief_state and hasattr(joint_belief_state, 'get_joint_probability'):
                    try:
                        # 获取当前节点的动作信息（如果有）
                        parent_idx = search_path.index(node) - 1
                        if parent_idx >= 0:
                            parent = search_path[parent_idx]
                            # 查找通过哪个动作到达当前节点
                            action = None
                            for act, child in parent.children.items():
                                if child == node:
                                    action = act
                                    break

                            if action is not None:
                                # 计算动作对应的联合信念概率
                                action_card_mapping = self._get_action_card_mapping(action)
                                if action_card_mapping:
                                    # 累计联合概率
                                    joint_prob_factor = 0.0
                                    prob_count = 0

                                    for card, weight in action_card_mapping.items():
                                        card_assignments = {node.player_id_to_act: card}
                                        joint_prob = joint_belief_state.get_joint_probability(card_assignments)
                                        if joint_prob > 0:
                                            joint_prob_factor += joint_prob * weight
                                            prob_count += 1

                                    # 应用联合概率因子增强更新
                                    if prob_count > 0:
                                        joint_prob_factor /= prob_count
                                        # 根据联合概率增强更新权重
                                        update_weight *= (1.0 + joint_prob_factor * 0.5)
                    except Exception as e:
                        # 联合信念计算失败，继续处理
                        logger.debug(f"反向传播中联合信念计算失败: {e}")

            # 考虑原始调整因素
            # 如果节点有信念状态相关信息，则调整值估计
            if hasattr(node, 'belief_confidence') and node.belief_confidence != 0.5:
                # 根据信念状态的置信度调整值估计
                # 高置信度时，增强值估计；低置信度时，降低值估计
                confidence_factor = 1.0 + 0.2 * (node.belief_confidence - 0.5)
                update_weight *= confidence_factor

            # 如果节点有信念熵信息，则进一步调整值估计
            if hasattr(node, 'belief_entropy') and node.belief_entropy > 0:
                # 更精细的熵调整：熵越高，越向平均值靠拢
                if node.belief_entropy > self.belief_entropy_high:
                    # 高熵时，值向中性值靠拢的程度取决于熵的程度
                    entropy_factor = (node.belief_entropy - self.belief_entropy_high) / (1.0 - self.belief_entropy_high)
                    # 应用更平滑的适应性调整
                    update_weight *= (1.0 - 0.2 * entropy_factor)

            # 如果启用了信息价值评估，则考虑信息价值
            if self.use_information_value and total_info_value > 0:
                # 信息价值随时间衰减，避免过度重复探索
                time_sensitive_factor = 1.0
                if node.visit_count > 10:
                    # 随着访问次数增加，信息价值权重降低
                    time_sensitive_factor = 10.0 / (10.0 + node.visit_count)

                # 将信息价值添加到更新权重中
                info_bonus = self.information_value_weight * total_info_value * time_sensitive_factor
                update_weight += info_bonus

            # 如果启用了内在动机，考虑内在奖励
            if self.use_intrinsic_motivation and hasattr(node, 'intrinsic_bonus') and node.intrinsic_bonus > 0:
                # 将内在奖励添加到更新权重中
                intrinsic_bonus = node.intrinsic_bonus * self.intrinsic_motivation_weight
                update_weight += intrinsic_bonus

            # 更新节点统计信息
            if hasattr(node, 'use_value_distribution') and node.use_value_distribution and is_distribution:
                # 如果节点使用值分布，并且值是分布，则更新分布
                if isinstance(node.value_sum, np.ndarray) and node.value_sum.shape == value_distribution.shape:
                    try:
                        # 检查value_distribution的有效性
                        if np.isnan(value_distribution).any() or np.isinf(value_distribution).any():
                            logger.warning(f"value_distribution包含NaN或inf值，跳过更新")
                            continue

                        # 检查update_weight的有效性
                        if np.isnan(update_weight) or np.isinf(update_weight):
                            logger.warning(f"update_weight为NaN或inf，使用默认值1.0")
                            update_weight = 1.0

                        # 安全地更新值分布
                        update_value = value_distribution * update_weight
                        if np.isnan(update_value).any() or np.isinf(update_value).any():
                            logger.warning(f"计算的update_value包含NaN或inf，跳过更新")
                            continue

                        # 更新值分布 (应用更新权重)
                        node.value_sum += update_value

                        # 检查更新后的value_sum状态
                        if np.isnan(node.value_sum).any() or np.isinf(node.value_sum).any():
                            logger.error(f"更新后的value_sum包含NaN或inf，重置为零数组")
                            node.value_sum = np.zeros_like(node.value_sum)

                    except Exception as e:
                        logger.error(f"更新值分布时发生错误: {e}")
                        # 如果更新失败，尝试使用标量更新
                        try:
                            if not (np.isnan(scalar_value) or np.isinf(scalar_value)):
                                node.value_sum += scalar_value * update_weight
                        except:
                            logger.error(f"标量更新也失败，跳过此次更新")
                            continue

                    # 如果启用了风险敏感决策，计算CVaR和风险敏感值
                    if self.use_risk_sensitive_decision and hasattr(self, 'cvar_calculator'):
                        try:
                            # 计算平均分布
                            avg_distribution = node.value_sum / (node.visit_count + 1)

                            # 将分布转换为张量
                            value_tensor = torch.FloatTensor(avg_distribution)

                            # 计算CVaR
                            cvar = self.cvar_calculator.compute_cvar(
                                value_tensor.unsqueeze(0),
                                alpha=self.risk_alpha
                            ).item()

                            # 计算风险敏感值
                            risk_value = self.cvar_calculator.compute_risk_sensitive_value(
                                value_tensor.unsqueeze(0),
                                alpha=self.risk_alpha,
                                beta=self.risk_beta
                            ).item()

                            # 存储CVaR和风险敏感值
                            node.cvar_value = cvar
                            node.risk_value = risk_value
                        except Exception as e:
                            # 如果计算失败，使用调整后的标量值
                            import logging
                            logging.warning(f"计算CVaR失败: {e}")
                else:
                    # 如果形状不匹配，使用标量更新
                    try:
                        if not (np.isnan(scalar_value) or np.isinf(scalar_value) or np.isnan(update_weight) or np.isinf(update_weight)):
                            node.value_sum += scalar_value * update_weight
                        else:
                            logger.warning(f"标量值或更新权重包含NaN或inf，跳过更新")
                    except Exception as e:
                        logger.error(f"标量更新失败: {e}")
            else:
                # 使用标量更新 (应用更新权重)
                try:
                    # 检查标量值和更新权重的有效性
                    if np.isnan(scalar_value) or np.isinf(scalar_value):
                        logger.warning(f"scalar_value为NaN或inf，使用0替代")
                        scalar_value = 0.0

                    if np.isnan(update_weight) or np.isinf(update_weight):
                        logger.warning(f"update_weight为NaN或inf，使用1.0替代")
                        update_weight = 1.0

                    # 计算更新值
                    update_value = scalar_value * update_weight
                    if np.isnan(update_value) or np.isinf(update_value):
                        logger.warning(f"计算的update_value为NaN或inf，跳过更新")
                    else:
                        # 检查当前value_sum的类型和状态
                        if isinstance(node.value_sum, torch.Tensor):
                            # 如果value_sum是tensor，确保它是有效的
                            if torch.isnan(node.value_sum).any() or torch.isinf(node.value_sum).any():
                                logger.warning(f"node.value_sum tensor包含NaN或inf，重置为0")
                                node.value_sum = torch.zeros_like(node.value_sum)

                            # 将标量转换为tensor进行更新
                            update_tensor = torch.tensor(update_value, dtype=node.value_sum.dtype, device=node.value_sum.device)
                            node.value_sum += update_tensor

                            # 检查更新后的状态
                            if torch.isnan(node.value_sum).any() or torch.isinf(node.value_sum).any():
                                logger.error(f"更新后的tensor value_sum包含NaN或inf，重置为0")
                                node.value_sum = torch.zeros_like(node.value_sum)
                        else:
                            # 标量或numpy数组更新
                            if isinstance(node.value_sum, np.ndarray):
                                if np.isnan(node.value_sum).any() or np.isinf(node.value_sum).any():
                                    logger.warning(f"node.value_sum numpy数组包含NaN或inf，重置为0")
                                    node.value_sum = np.zeros_like(node.value_sum)
                            elif np.isnan(node.value_sum) or np.isinf(node.value_sum):
                                logger.warning(f"node.value_sum标量为NaN或inf，重置为0")
                                node.value_sum = 0.0

                            node.value_sum += update_value

                            # 检查更新后的状态
                            if isinstance(node.value_sum, np.ndarray):
                                if np.isnan(node.value_sum).any() or np.isinf(node.value_sum).any():
                                    logger.error(f"更新后的numpy value_sum包含NaN或inf，重置为0")
                                    node.value_sum = np.zeros_like(node.value_sum)
                            elif np.isnan(node.value_sum) or np.isinf(node.value_sum):
                                logger.error(f"更新后的标量value_sum为NaN或inf，重置为0")
                                node.value_sum = 0.0

                except Exception as e:
                    logger.error(f"标量更新时发生错误: {e}")
                    logger.error(f"scalar_value: {scalar_value}, update_weight: {update_weight}")
                    logger.error(f"node.value_sum类型: {type(node.value_sum)}")
                    # 如果更新失败，确保value_sum处于安全状态
                    if isinstance(node.value_sum, torch.Tensor):
                        node.value_sum = torch.zeros_like(node.value_sum)
                    elif isinstance(node.value_sum, np.ndarray):
                        node.value_sum = np.zeros_like(node.value_sum)
                    else:
                        node.value_sum = 0.0

            # 更新访问次数
            node.visit_count += 1

            # 计算下一个节点的值估计
            if is_distribution:
                # 如果是分布，使用折扣后的分布
                value_distribution = node.reward + discount * value_distribution
                scalar_value = node.reward + discount * scalar_value
            else:
                # 使用标量
                scalar_value = node.reward + discount * scalar_value

        # 返回标量值
        return scalar_value

    def _sample_from_belief(self, belief_state: Any) -> Dict[str, bool]:
        """
        从信念状态分布中采样对手手牌

        Args:
            belief_state (Any): 信念状态对象，包含牌的概率分布

        Returns:
            Dict[str, bool]: 牌到布尔值的映射，表示牌是否在手中
        """
        # 如果信念状态无效或置信度低于阈值，返回空字典
        if not belief_state or getattr(belief_state, 'confidence', 0) < self.belief_confidence_threshold:
            return {}

        # 获取牌的概率分布
        card_probs = getattr(belief_state, 'card_probabilities', {})
        if not card_probs:
            return {}

        # 使用直接概率采样
        sampled_cards = {}
        for card, prob in card_probs.items():
            # 根据概率决定牌是否在手中
            is_in_hand = random.random() < prob
            sampled_cards[card] = is_in_hand

        return sampled_cards

    def _belief_simulation(self, state: Any, belief_trackers: Dict[str, Any], depth: int = 0, max_depth: int = 5) -> float:
        """
        使用信念状态采样进行模拟

        Args:
            state (Any): 当前游戏状态
            belief_trackers (Dict[str, Any]): 信念追踪器字典，键为玩家ID
            depth (int, optional): 当前深度. Defaults to 0.
            max_depth (int, optional): 最大深度. Defaults to 5.

        Returns:
            float: 模拟的价值估计
        """
        # 如果达到模拟深度限制或游戏结束，返回奖励
        if depth >= max_depth or state.is_terminal():
            return state.get_reward() if hasattr(state, 'get_reward') else 0.0

        # 获取当前玩家ID
        player_id = None
        if hasattr(state, 'current_player'):
            player_id = state.current_player
        elif hasattr(state, 'to_play'):
            player_id = state.to_play
        else:
            # 尝试找到其他可能的玩家ID属性
            for attr in ['player_to_act', 'current_player_id', 'active_player']:
                if hasattr(state, attr):
                    player_id = getattr(state, attr)
                    break

        # 如果没找到玩家ID，使用简单模拟
        if player_id is None:
            return self._simple_rollout(state, depth, max_depth)

        # 检查当前玩家的信念追踪器
        belief_state = None
        if player_id in belief_trackers:
            belief_tracker = belief_trackers[player_id]
            # 获取信念状态
            if hasattr(belief_tracker, 'get_belief_state'):
                belief_state = belief_tracker.get_belief_state()

        # 如果没有有效的信念状态或置信度低于阈值，使用简单模拟
        if not belief_state or getattr(belief_state, 'confidence', 0) < self.belief_confidence_threshold:
            return self._simple_rollout(state, depth, max_depth)

        # 获取对手ID列表(可能有多个对手)
        opponent_ids = []
        if hasattr(state, 'get_opponents'):
            opponent_ids = state.get_opponents(player_id)
        elif hasattr(state, 'player_ids'):
            # 如果有玩家ID列表，排除当前玩家
            all_players = state.player_ids
            opponent_ids = [p for p in all_players if p != player_id]
        elif hasattr(state, 'get_other_player_id'):
            # 简单的双人博弈
            opponent_id = state.get_other_player_id(player_id)
            if opponent_id:
                opponent_ids = [opponent_id]

        # 采样对手手牌，创建一个基于信念的确定性状态
        sampled_state = state

        # 检查是否有联合信念状态
        joint_belief_state = None
        if hasattr(belief_trackers, 'get_joint_belief_state'):
            joint_belief_state = belief_trackers.get_joint_belief_state()
            # 使用联合信念状态采样
            if joint_belief_state and hasattr(joint_belief_state, 'sample_joint_assignment'):
                try:
                    # 使用联合信念采样所有玩家的牌
                    assignments = joint_belief_state.sample_joint_assignment(num_samples=1)[0]
                    # 如果状态支持设置手牌分配
                    if hasattr(state, 'with_card_assignments'):
                        sampled_state = state.with_card_assignments(assignments)
                    elif all(hasattr(state, f'with_{pid}_cards') for pid in assignments):
                        # 逐个玩家设置手牌
                        for pid, cards in assignments.items():
                            set_cards_method = getattr(state, f'with_{pid}_cards')
                            sampled_state = set_cards_method(cards)
                except Exception as e:
                    logger.debug(f"联合信念采样失败: {e}")
                    # 回退到单独采样

        # 如果没有使用联合信念采样，则单独采样每个对手
        if sampled_state == state and opponent_ids:
            # 处理每个对手
            for opponent_id in opponent_ids:
                # 检查是否有该对手的信念追踪器
                if opponent_id in belief_trackers:
                    opponent_belief_tracker = belief_trackers[opponent_id]
                    if hasattr(opponent_belief_tracker, 'get_belief_state'):
                        opponent_belief = opponent_belief_tracker.get_belief_state()
                        if opponent_belief and hasattr(opponent_belief, 'card_probabilities'):
                            # 根据信念状态采样对手手牌
                            sampled_cards = {}
                            for card, prob in opponent_belief.card_probabilities.items():
                                if random.random() < prob:
                                    sampled_cards[card] = True

                            # 如果状态支持设置手牌
                            if hasattr(state, f'with_{opponent_id}_cards'):
                                # 使用特定方法设置对手手牌
                                set_cards_method = getattr(state, f'with_{opponent_id}_cards')
                                sampled_state = set_cards_method(list(sampled_cards.keys()))
                            elif hasattr(state, 'with_sampled_cards'):
                                # 通用方法设置手牌
                                sampled_state = state.with_sampled_cards(opponent_id, sampled_cards)

        # 确保采样后的状态仍是一个有效状态
        if not sampled_state:
            sampled_state = state

        # 获取合法动作
        legal_actions = []
        if hasattr(sampled_state, 'get_legal_actions'):
            legal_actions = sampled_state.get_legal_actions()
        elif hasattr(sampled_state, 'legal_actions'):
            legal_actions = sampled_state.legal_actions

        # 如果没有合法动作，返回当前状态的奖励
        if not legal_actions:
            return sampled_state.get_reward() if hasattr(sampled_state, 'get_reward') else 0.0

        # 使用基于信念的策略选择动作
        action = self._select_action_with_belief(sampled_state, belief_state, legal_actions)

        # 应用动作获取下一个状态
        next_state = None
        if hasattr(sampled_state, 'apply_action'):
            next_state = sampled_state.apply_action(action)
        elif hasattr(sampled_state, 'step'):
            next_state, reward, done = sampled_state.step(action)

        # 如果获取下一个状态失败，返回当前状态的奖励
        if not next_state:
            return sampled_state.get_reward() if hasattr(sampled_state, 'get_reward') else 0.0

        # 获取当前状态的奖励
        immediate_reward = 0.0
        if hasattr(sampled_state, 'get_reward'):
            immediate_reward = sampled_state.get_reward()
        elif hasattr(sampled_state, 'get_reward_for'):
            immediate_reward = sampled_state.get_reward_for(player_id)

        # 递归模拟
        future_reward = self._belief_simulation(next_state, belief_trackers, depth + 1, max_depth)

        # 返回打折后的总奖励
        return immediate_reward + self.discount * future_reward

    def _simple_rollout(self, state: Any, depth: int = 0, max_depth: int = 5) -> float:
        """
        简单的随机rollout，当信念状态不可用时使用

        Args:
            state (Any): 当前游戏状态
            depth (int, optional): 当前深度. Defaults to 0.
            max_depth (int, optional): 最大深度. Defaults to 5.

        Returns:
            float: 模拟的价值估计
        """
        # 如果达到模拟深度限制或游戏结束，返回奖励
        if depth >= max_depth or state.is_terminal():
            return state.get_reward() if hasattr(state, 'get_reward') else 0.0

        # 获取合法动作
        legal_actions = []
        if hasattr(state, 'get_legal_actions'):
            legal_actions = state.get_legal_actions()
        elif hasattr(state, 'legal_actions'):
            legal_actions = state.legal_actions

        # 如果没有合法动作，返回当前状态的奖励
        if not legal_actions:
            return state.get_reward() if hasattr(state, 'get_reward') else 0.0

        # 随机选择动作
        action = random.choice(legal_actions)

        # 应用动作获取下一个状态
        next_state = None
        if hasattr(state, 'apply_action'):
            next_state = state.apply_action(action)
        elif hasattr(state, 'step'):
            next_state, reward, done = state.step(action)

        # 如果获取下一个状态失败，返回当前状态的奖励
        if not next_state:
            return state.get_reward() if hasattr(state, 'get_reward') else 0.0

        # 获取当前状态的奖励
        immediate_reward = 0.0
        if hasattr(state, 'get_reward'):
            immediate_reward = state.get_reward()
        elif hasattr(state, 'get_reward_for'):
            # 获取当前玩家
            player_id = None
            if hasattr(state, 'current_player'):
                player_id = state.current_player
            elif hasattr(state, 'to_play'):
                player_id = state.to_play

            if player_id:
                immediate_reward = state.get_reward_for(player_id)

        # 递归模拟
        future_reward = self._simple_rollout(next_state, depth + 1, max_depth)

        # 返回打折后的总奖励
        return immediate_reward + self.discount * future_reward

    def _select_action_with_belief(self, state: Any, belief_state: Any, legal_actions: List[int]) -> int:
        """
        基于信念状态选择动作，用于信念模拟

        Args:
            state (Any): 当前游戏状态
            belief_state (Any): 信念状态
            legal_actions (List[int]): 合法动作列表

        Returns:
            int: 选择的动作
        """
        # 如果信念状态无效或没有概率分布，使用随机选择
        if not belief_state or not hasattr(belief_state, 'card_probabilities'):
            return random.choice(legal_actions)

        # 获取信念状态的置信度和概率分布
        confidence = getattr(belief_state, 'confidence', 0.5)
        card_probs = belief_state.card_probabilities

        # 根据信念状态评估每个动作
        action_scores = {}
        for action in legal_actions:
            # 获取动作与牌的映射关系
            action_card_mapping = self._get_action_card_mapping(action)

            if not action_card_mapping:
                # 如果没有映射关系，使用均匀分数
                action_scores[action] = 0.5
                continue

            # 计算加权分数
            weighted_score = 0.0
            total_weight = 0.0

            for card, weight in action_card_mapping.items():
                if card in card_probs:
                    weighted_score += card_probs[card] * weight
                    total_weight += weight

            # 归一化分数
            if total_weight > 0:
                action_scores[action] = weighted_score / total_weight
            else:
                action_scores[action] = 0.5

        # 根据置信度混合随机选择和贪心选择
        if random.random() < confidence:
            # 高置信度，更倾向于选择最高分数的动作
            # 使用softmax选择
            temperature = max(0.1, 1.0 - confidence)  # 置信度越高，温度越低
            return self._softmax_selection(action_scores, temperature)
        else:
            # 低置信度，更倾向于随机选择
            # 使用ε-贪心策略
            epsilon = max(0.1, 1.0 - confidence)
            if random.random() < epsilon:
                return random.choice(legal_actions)
            else:
                return max(action_scores.items(), key=lambda x: x[1])[0]

    def _softmax_selection(self, action_scores: Dict[int, float], temperature: float = 1.0) -> int:
        """
        使用softmax策略选择动作

        Args:
            action_scores (Dict[int, float]): 动作分数字典
            temperature (float, optional): 温度参数. Defaults to 1.0.

        Returns:
            int: 选择的动作
        """
        # 如果只有一个动作，直接返回
        if len(action_scores) == 1:
            return list(action_scores.keys())[0]

        # 计算softmax概率
        keys = list(action_scores.keys())
        values = np.array([action_scores[k] for k in keys])

        # 应用温度
        if temperature > 0:
            values = values / temperature

        # 计算softmax
        max_value = np.max(values)
        exp_values = np.exp(values - max_value)
        probs = exp_values / np.sum(exp_values)

        # 按概率选择动作
        choice_idx = np.random.choice(len(keys), p=probs)
        return keys[choice_idx]