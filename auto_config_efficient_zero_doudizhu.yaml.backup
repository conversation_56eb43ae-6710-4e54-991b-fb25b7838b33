checkpoint:
  auto_resume: true
  max_to_keep: 5
  save_dir: checkpoints
  save_frequency: 1000
device:
  benchmark: true
  deterministic: false
  ids:
  - 0
  mixed_precision: true
  type: cuda
distributed:
  backend: null
  enabled: false
  init_method: null
  rank: 0
  world_size: 1
efficient_zero:
  model_buffer_size: 100000
  reanalyze_ratio: 0.4
  replay_buffer_size: 200000
  td_steps: 3
  unroll_steps: 5
  use_priority: true
  value_prefix: true
hardware:
  cpu:
    architecture: AMD64
    cores: 8
    threads: 16
  gpu:
    count: 1
    ids:
    - 0
    memory_gb: 20.0
    model: NVIDIA GeForce RTX 3080
    total_memory_gb: 20.0
  memory:
    available_gb: 40.55
    total_gb: 63.93
  system:
    is_linux: false
    is_windows: true
    type: Windows
logging:
  level: INFO
  save_dir: logs
  tensorboard: true
  wandb: false
mcts:
  batch_size_inference: 32
  c_puct: 1.25
  dirichlet_alpha: 0.3
  exploration_fraction: 0.25
  num_simulations: 50
  parallel_threads: 8
memory:
  cache_size_gb: 8
  cpu_offload: false
  gradient_checkpointing: false
  non_blocking: true
  persistent_workers: true
  pin_memory: true
meta:
  config_version: 1.0.0
  generated_by: AutoConfigManager
  hardware_profile: 1gpu_nvidia_geforce_rtx_3080
  optimization_level: basic
project:
  algorithm: efficient_zero
  auto_generated: true
  environment: doudizhu
  name: 斗地主AI训练
training:
  batch_size: 1024
  epochs: 1000
  gradient_accumulation_steps: 1
  learning_rate: 0.001
  num_workers: 3
  optimizer:
    betas:
    - 0.9
    - 0.999
    name: AdamW
    weight_decay: 0.01
  prefetch_factor: 4
  scheduler:
    T_0: 100
    T_mult: 2
    eta_min: 1e-6
    name: CosineAnnealingWarmRestarts
